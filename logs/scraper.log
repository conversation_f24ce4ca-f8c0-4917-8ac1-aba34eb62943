2025-08-05 19:47:03,566 - src.utils.monitoring - INFO - Scraper monitoring system initialized
2025-08-05 19:47:03,566 - src.utils.fallback_system - DEBUG - Loaded fallback rule for bbb
2025-08-05 19:47:03,566 - src.utils.fallback_system - DEBUG - Loaded fallback rule for manta
2025-08-05 19:47:03,566 - src.utils.fallback_system - DEBUG - Loaded fallback rule for linkedin
2025-08-05 19:47:03,567 - src.utils.fallback_system - DEBUG - Loaded fallback rule for truepeoplesearch
2025-08-05 19:47:03,567 - src.utils.fallback_system - DEBUG - Loaded fallback rule for bbb
2025-08-05 19:47:03,567 - src.utils.fallback_system - DEBUG - Loaded fallback rule for manta
2025-08-05 19:47:03,567 - src.utils.fallback_system - INFO - Fallback system initialized
2025-08-05 19:47:03,567 - src.utils.fallback_system - INFO - Health checker initialized
2025-08-05 19:47:03,567 - src.utils.fallback_system - INFO - Alert system initialized
2025-08-05 19:47:03,567 - src.utils.monitoring - INFO - Scraper monitoring started
2025-08-05 19:47:03,567 - __main__ - INFO - Monitoring system started
2025-08-05 19:47:03,569 - src.utils.google_custom_search - WARNING - Google Custom Search API key not provided
2025-08-05 19:47:03,569 - src.utils.google_custom_search - WARNING - Google Custom Search Engine ID not provided
2025-08-05 19:47:03,569 - src.utils.google_custom_search - WARNING - Google Custom Search API is disabled
2025-08-05 19:47:03,569 - src.utils.google_custom_search - INFO - Google search fallback disabled
2025-08-05 19:47:03,569 - src.utils.google_search - WARNING - Google Custom Search API not available - using fallback
2025-08-05 19:47:03,569 - src.utils.google_search - WARNING - For better results, configure Google Custom Search API
2025-08-05 19:47:03,569 - src.scrapers.base_scraper.bbb - INFO - Enhanced Cloudflare bypass enabled for BBB
2025-08-05 19:47:03,570 - src.utils.google_custom_search - WARNING - Google Custom Search API key not provided
2025-08-05 19:47:03,570 - src.utils.google_custom_search - WARNING - Google Custom Search Engine ID not provided
2025-08-05 19:47:03,570 - src.utils.google_custom_search - WARNING - Google Custom Search API is disabled
2025-08-05 19:47:03,570 - src.utils.google_custom_search - INFO - Google search fallback disabled
2025-08-05 19:47:03,570 - src.utils.google_search - WARNING - Google Custom Search API not available - using fallback
2025-08-05 19:47:03,570 - src.utils.google_search - WARNING - For better results, configure Google Custom Search API
2025-08-05 19:47:03,570 - src.scrapers.base_scraper.manta - INFO - Enhanced Cloudflare bypass enabled for Manta
2025-08-05 19:47:03,572 - src.scrapers.truthfinder_scraper - INFO - TruthFinder API scraper initialized successfully
2025-08-05 19:47:03,572 - __main__ - INFO - LinkedIn scraper disabled (recommended for production)
2025-08-05 19:47:03,572 - src.utils.monitoring - INFO - Registered scraper for monitoring: bbb
2025-08-05 19:47:03,572 - src.utils.fallback_system - DEBUG - Registered health check: bbb_health
2025-08-05 19:47:03,572 - src.utils.monitoring - INFO - Registered scraper for monitoring: manta
2025-08-05 19:47:03,572 - src.utils.fallback_system - DEBUG - Registered health check: manta_health
2025-08-05 19:47:03,572 - src.utils.monitoring - INFO - Registered scraper for monitoring: truepeoplesearch
2025-08-05 19:47:03,572 - src.utils.fallback_system - DEBUG - Registered health check: truepeoplesearch_health
2025-08-05 19:47:03,572 - src.utils.monitoring - INFO - Registered scraper for monitoring: cyberbackgroundchecks
2025-08-05 19:47:03,572 - src.utils.fallback_system - DEBUG - Registered health check: cyberbackgroundchecks_health
2025-08-05 19:47:03,572 - src.utils.monitoring - INFO - Registered scraper for monitoring: truthfinder_api
2025-08-05 19:47:03,573 - src.utils.fallback_system - DEBUG - Registered health check: truthfinder_api_health
2025-08-05 19:47:03,573 - __main__ - INFO - Starting scrape for 1 business types, 1 locations, using 4 sources
2025-08-05 19:47:03,573 - __main__ - INFO - [1/4] Scraping bbb for 'restaurant' in 'houston tx'
2025-08-05 19:47:03,573 - src.scrapers.base_scraper.bbb - INFO - Searching BBB via Google for 'restaurant' owners in 'houston tx'
2025-08-05 19:47:03,573 - src.scrapers.base_scraper.bbb - INFO - Found 0 BBB listings from Google search
2025-08-05 19:47:03,573 - __main__ - INFO - Found 0 results from bbb
2025-08-05 19:47:03,573 - __main__ - INFO - [2/4] Scraping manta for 'restaurant' in 'houston tx'
2025-08-05 19:47:03,573 - src.scrapers.base_scraper.manta - INFO - Searching Manta via Google for 'restaurant' owners in 'houston tx'
2025-08-05 19:47:03,573 - src.scrapers.base_scraper.manta - INFO - Found 0 Manta listings from Google search
2025-08-05 19:47:03,573 - __main__ - INFO - Found 0 results from manta
2025-08-05 19:47:03,573 - __main__ - INFO - [3/4] Scraping cyberbackgroundchecks for 'restaurant' in 'houston tx'
2025-08-05 19:47:03,573 - src.scrapers.base_scraper.cyberbackgroundchecks - INFO - Searching CyberBackgroundChecks for 'restaurant' owners in 'houston tx'
2025-08-05 19:47:03,573 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 1 for https://www.cyberbackgroundchecks.com/search?q=restaurant+owner&location=houston+tx
2025-08-05 19:47:06,841 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:47:06,934 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+owner&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:47:06,936 - src.utils.anti_bot - WARNING - Strategy 1 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+owner&location=houston+tx
2025-08-05 19:47:06,936 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 2 for https://www.cyberbackgroundchecks.com/search?q=restaurant+owner&location=houston+tx
2025-08-05 19:47:15,866 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:47:15,982 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+owner&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:47:15,985 - src.utils.anti_bot - WARNING - Strategy 2 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+owner&location=houston+tx
2025-08-05 19:47:15,985 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 3 for https://www.cyberbackgroundchecks.com/search?q=restaurant+owner&location=houston+tx
2025-08-05 19:47:16,014 - undetected_chromedriver.patcher - DEBUG - getting release number from /last-known-good-versions-with-downloads.json
2025-08-05 19:47:16,237 - undetected_chromedriver.patcher - DEBUG - downloading from https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/138.0.7204.183/mac-x64/chromedriver-mac-x64.zip
2025-08-05 19:47:20,822 - undetected_chromedriver.patcher - DEBUG - unzipping /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmpmfsvgco4
2025-08-05 19:47:20,934 - undetected_chromedriver.patcher - INFO - patching driver executable /Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver
2025-08-05 19:47:20,954 - undetected_chromedriver.patcher - DEBUG - found block:
b'{window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy = window.Proxy;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;window.cdc_adoQpoasnfa76pfcZLmcfl_JSON = window.JSON;window.cdc_adoQpoasnfa76pfcZLmcfl_Window = window.Window;}'
replacing with:
b'{console.log("undetected chromedriver 1337!")}                                                                                                                                                                                                                                                                                                                                                             '
2025-08-05 19:47:20,960 - undetected_chromedriver.patcher - DEBUG - patching took us 0.03 seconds
2025-08-05 19:47:21,014 - selenium.webdriver.common.service - DEBUG - Started executable: `/Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver` in a child process with pid: 11264
2025-08-05 19:47:26,572 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55496/session {"capabilities": {"firstMatch": [{}], "alwaysMatch": {"browserName": "chrome", "pageLoadStrategy": "normal", "goog:chromeOptions": {"extensions": [], "binary": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "args": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-blink-features=AutomationControlled", "--disable-extensions", "--disable-plugins", "--disable-images", "--disable-javascript", "--disable-gpu", "--no-first-run", "--no-default-browser-check", "--disable-default-apps", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "--window-size=1470,1067", "--remote-debugging-host=127.0.0.1", "--remote-debugging-port=55495", "--user-data-dir=/var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmpg1q7g_e7", "--lang=en-US", "--no-default-browser-check", "--no-first-run", "--no-sandbox", "--test-type", "--window-size=1920,1080", "--start-maximized", "--no-sandbox", "--log-level=0"], "debuggerAddress": "127.0.0.1:55495"}}}}
2025-08-05 19:47:26,575 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:55496
2025-08-05 19:47:26,640 - urllib3.connectionpool - DEBUG - http://localhost:55496 "POST /session HTTP/1.1" 200 0
2025-08-05 19:47:26,640 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.184","chrome":{"chromedriverVersion":"138.0.7204.183 (e90faf484ddbc033fc9bf337621761d3dd5c5289-refs/branch-heads/7204@{#2435})"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"127.0.0.1:55495"},"pageLoadStrategy":"normal","platformName":"","proxy":{},"setWindowRect":false,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"c8fd883e8c5082d1f4e4ce5a4a49df6e"}} | headers=HTTPHeaderDict({'Content-Length': '762', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:26,640 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:26,640 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55496/session/c8fd883e8c5082d1f4e4ce5a4a49df6e/execute/sync {"script": "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})", "args": []}
2025-08-05 19:47:26,650 - urllib3.connectionpool - DEBUG - http://localhost:55496 "POST /session/c8fd883e8c5082d1f4e4ce5a4a49df6e/execute/sync HTTP/1.1" 200 0
2025-08-05 19:47:26,650 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:26,651 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:26,651 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55496/session/c8fd883e8c5082d1f4e4ce5a4a49df6e/execute/sync {"script": "\n            Object.defineProperty(navigator, 'plugins', {\n                get: () => [1, 2, 3, 4, 5]\n            });\n            ", "args": []}
2025-08-05 19:47:26,654 - urllib3.connectionpool - DEBUG - http://localhost:55496 "POST /session/c8fd883e8c5082d1f4e4ce5a4a49df6e/execute/sync HTTP/1.1" 200 0
2025-08-05 19:47:26,654 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:26,655 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:26,655 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55496/session/c8fd883e8c5082d1f4e4ce5a4a49df6e/execute/sync {"script": "\n            Object.defineProperty(navigator, 'languages', {\n                get: () => ['en-US', 'en']\n            });\n            ", "args": []}
2025-08-05 19:47:26,659 - urllib3.connectionpool - DEBUG - http://localhost:55496 "POST /session/c8fd883e8c5082d1f4e4ce5a4a49df6e/execute/sync HTTP/1.1" 200 0
2025-08-05 19:47:26,659 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:26,659 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:26,659 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55496/session/c8fd883e8c5082d1f4e4ce5a4a49df6e/execute/sync {"script": "\n            const originalQuery = window.navigator.permissions.query;\n            return window.navigator.permissions.query = (parameters) => (\n                parameters.name === 'notifications' ?\n                    Promise.resolve({ state: Notification.permission }) :\n                    originalQuery(parameters)\n            );\n            ", "args": []}
2025-08-05 19:47:26,662 - urllib3.connectionpool - DEBUG - http://localhost:55496 "POST /session/c8fd883e8c5082d1f4e4ce5a4a49df6e/execute/sync HTTP/1.1" 200 0
2025-08-05 19:47:26,663 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{}} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:26,663 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:26,663 - src.utils.anti_bot - INFO - Using Selenium to bypass Cloudflare for https://www.cyberbackgroundchecks.com/search?q=restaurant+owner&location=houston+tx
2025-08-05 19:47:26,663 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55496/session/c8fd883e8c5082d1f4e4ce5a4a49df6e/url {"url": "https://www.cyberbackgroundchecks.com/search?q=restaurant+owner&location=houston+tx"}
2025-08-05 19:47:26,970 - urllib3.connectionpool - DEBUG - http://localhost:55496 "POST /session/c8fd883e8c5082d1f4e4ce5a4a49df6e/url HTTP/1.1" 200 0
2025-08-05 19:47:26,970 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:26,970 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:26,971 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:55496/session/c8fd883e8c5082d1f4e4ce5a4a49df6e/source {}
2025-08-05 19:47:26,975 - urllib3.connectionpool - DEBUG - http://localhost:55496 "GET /session/c8fd883e8c5082d1f4e4ce5a4a49df6e/source HTTP/1.1" 200 0
2025-08-05 19:47:26,975 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6eb06988d31ed\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6eb06988d31ed',t:'MTc1NDQwMzQ0Ni4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:26,976 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:32,129 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:55496/session/c8fd883e8c5082d1f4e4ce5a4a49df6e/source {}
2025-08-05 19:47:32,138 - urllib3.connectionpool - DEBUG - http://localhost:55496 "GET /session/c8fd883e8c5082d1f4e4ce5a4a49df6e/source HTTP/1.1" 200 0
2025-08-05 19:47:32,138 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6eb06988d31ed\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6eb06988d31ed',t:'MTc1NDQwMzQ0Ni4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:32,139 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:32,139 - src.utils.anti_bot - INFO - Successfully bypassed Cloudflare with Selenium for https://www.cyberbackgroundchecks.com/search?q=restaurant+owner&location=houston+tx
2025-08-05 19:47:32,218 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:47:32,224 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:47:32,230 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:47:32,235 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:47:32,242 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:47:32,247 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:47:32,252 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:47:32,266 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:47:32,267 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:47:32,267 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:47:32,268 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.5976149302422976
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:47:32,269 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:47:32,269 - src.utils.anti_bot - INFO - Cloudflare bypass successful for https://www.cyberbackgroundchecks.com/search?q=restaurant+owner&location=houston+tx
2025-08-05 19:47:32,304 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:47:32,309 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:47:32,314 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:47:32,320 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:47:32,325 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:47:32,331 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:47:32,337 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:47:32,351 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.5976149302422976
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:47:32,352 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:47:34,484 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 1 for https://www.cyberbackgroundchecks.com/search?q=restaurant+CEO&location=houston+tx
2025-08-05 19:47:36,720 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:47:36,837 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+CEO&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:47:36,839 - src.utils.anti_bot - WARNING - Strategy 1 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+CEO&location=houston+tx
2025-08-05 19:47:36,839 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 2 for https://www.cyberbackgroundchecks.com/search?q=restaurant+CEO&location=houston+tx
2025-08-05 19:47:43,325 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:47:43,455 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+CEO&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:47:43,458 - src.utils.anti_bot - WARNING - Strategy 2 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+CEO&location=houston+tx
2025-08-05 19:47:43,458 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 3 for https://www.cyberbackgroundchecks.com/search?q=restaurant+CEO&location=houston+tx
2025-08-05 19:47:43,462 - undetected_chromedriver.patcher - DEBUG - getting release number from /last-known-good-versions-with-downloads.json
2025-08-05 19:47:43,636 - undetected_chromedriver.patcher - DEBUG - downloading from https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/138.0.7204.183/mac-x64/chromedriver-mac-x64.zip
2025-08-05 19:47:50,656 - undetected_chromedriver.patcher - DEBUG - unzipping /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmpi6_hckpa
2025-08-05 19:47:50,766 - undetected_chromedriver.patcher - INFO - patching driver executable /Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver
2025-08-05 19:47:50,786 - undetected_chromedriver.patcher - DEBUG - found block:
b'{window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy = window.Proxy;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;window.cdc_adoQpoasnfa76pfcZLmcfl_JSON = window.JSON;window.cdc_adoQpoasnfa76pfcZLmcfl_Window = window.Window;}'
replacing with:
b'{console.log("undetected chromedriver 1337!")}                                                                                                                                                                                                                                                                                                                                                             '
2025-08-05 19:47:50,795 - undetected_chromedriver.patcher - DEBUG - patching took us 0.03 seconds
2025-08-05 19:47:50,814 - selenium.webdriver.common.service - DEBUG - Started executable: `/Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver` in a child process with pid: 11378
2025-08-05 19:47:56,375 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55689/session {"capabilities": {"firstMatch": [{}], "alwaysMatch": {"browserName": "chrome", "pageLoadStrategy": "normal", "goog:chromeOptions": {"extensions": [], "binary": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "args": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-blink-features=AutomationControlled", "--disable-extensions", "--disable-plugins", "--disable-images", "--disable-javascript", "--disable-gpu", "--no-first-run", "--no-default-browser-check", "--disable-default-apps", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********", "--window-size=1785,909", "--remote-debugging-host=127.0.0.1", "--remote-debugging-port=55688", "--user-data-dir=/var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmpv0n1392j", "--lang=en-US", "--no-default-browser-check", "--no-first-run", "--no-sandbox", "--test-type", "--window-size=1920,1080", "--start-maximized", "--no-sandbox", "--log-level=0"], "debuggerAddress": "127.0.0.1:55688"}}}}
2025-08-05 19:47:56,379 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:55689
2025-08-05 19:47:56,458 - urllib3.connectionpool - DEBUG - http://localhost:55689 "POST /session HTTP/1.1" 200 0
2025-08-05 19:47:56,458 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.184","chrome":{"chromedriverVersion":"138.0.7204.183 (e90faf484ddbc033fc9bf337621761d3dd5c5289-refs/branch-heads/7204@{#2435})"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"127.0.0.1:55688"},"pageLoadStrategy":"normal","platformName":"","proxy":{},"setWindowRect":false,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"18891e33cafcd8e8e95b971a3d0124fb"}} | headers=HTTPHeaderDict({'Content-Length': '762', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:56,459 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:56,459 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55689/session/18891e33cafcd8e8e95b971a3d0124fb/execute/sync {"script": "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})", "args": []}
2025-08-05 19:47:56,471 - urllib3.connectionpool - DEBUG - http://localhost:55689 "POST /session/18891e33cafcd8e8e95b971a3d0124fb/execute/sync HTTP/1.1" 200 0
2025-08-05 19:47:56,471 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:56,471 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:56,471 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55689/session/18891e33cafcd8e8e95b971a3d0124fb/execute/sync {"script": "\n            Object.defineProperty(navigator, 'plugins', {\n                get: () => [1, 2, 3, 4, 5]\n            });\n            ", "args": []}
2025-08-05 19:47:56,474 - urllib3.connectionpool - DEBUG - http://localhost:55689 "POST /session/18891e33cafcd8e8e95b971a3d0124fb/execute/sync HTTP/1.1" 200 0
2025-08-05 19:47:56,474 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:56,475 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:56,475 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55689/session/18891e33cafcd8e8e95b971a3d0124fb/execute/sync {"script": "\n            Object.defineProperty(navigator, 'languages', {\n                get: () => ['en-US', 'en']\n            });\n            ", "args": []}
2025-08-05 19:47:56,478 - urllib3.connectionpool - DEBUG - http://localhost:55689 "POST /session/18891e33cafcd8e8e95b971a3d0124fb/execute/sync HTTP/1.1" 200 0
2025-08-05 19:47:56,478 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:56,478 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:56,478 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55689/session/18891e33cafcd8e8e95b971a3d0124fb/execute/sync {"script": "\n            const originalQuery = window.navigator.permissions.query;\n            return window.navigator.permissions.query = (parameters) => (\n                parameters.name === 'notifications' ?\n                    Promise.resolve({ state: Notification.permission }) :\n                    originalQuery(parameters)\n            );\n            ", "args": []}
2025-08-05 19:47:56,482 - urllib3.connectionpool - DEBUG - http://localhost:55689 "POST /session/18891e33cafcd8e8e95b971a3d0124fb/execute/sync HTTP/1.1" 200 0
2025-08-05 19:47:56,482 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{}} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:56,482 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:56,482 - src.utils.anti_bot - INFO - Using Selenium to bypass Cloudflare for https://www.cyberbackgroundchecks.com/search?q=restaurant+CEO&location=houston+tx
2025-08-05 19:47:56,482 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55689/session/18891e33cafcd8e8e95b971a3d0124fb/url {"url": "https://www.cyberbackgroundchecks.com/search?q=restaurant+CEO&location=houston+tx"}
2025-08-05 19:47:56,716 - urllib3.connectionpool - DEBUG - http://localhost:55689 "POST /session/18891e33cafcd8e8e95b971a3d0124fb/url HTTP/1.1" 200 0
2025-08-05 19:47:56,716 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:56,716 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:47:56,717 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:55689/session/18891e33cafcd8e8e95b971a3d0124fb/source {}
2025-08-05 19:47:56,720 - urllib3.connectionpool - DEBUG - http://localhost:55689 "GET /session/18891e33cafcd8e8e95b971a3d0124fb/source HTTP/1.1" 200 0
2025-08-05 19:47:56,720 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6ebc0dc833df7\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6ebc0dc833df7',t:'MTc1NDQwMzQ3Ni4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:47:56,721 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:01,694 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:55689/session/18891e33cafcd8e8e95b971a3d0124fb/source {}
2025-08-05 19:48:01,699 - urllib3.connectionpool - DEBUG - http://localhost:55689 "GET /session/18891e33cafcd8e8e95b971a3d0124fb/source HTTP/1.1" 200 0
2025-08-05 19:48:01,700 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6ebc0dc833df7\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6ebc0dc833df7',t:'MTc1NDQwMzQ3Ni4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:48:01,701 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:01,701 - src.utils.anti_bot - INFO - Successfully bypassed Cloudflare with Selenium for https://www.cyberbackgroundchecks.com/search?q=restaurant+CEO&location=houston+tx
2025-08-05 19:48:01,936 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:48:01,947 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:48:01,960 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:48:01,967 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:48:01,976 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:48:01,982 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:48:01,987 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:48:02,001 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.594497105938526
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:48:02,002 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:48:02,003 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:48:02,003 - src.utils.anti_bot - INFO - Cloudflare bypass successful for https://www.cyberbackgroundchecks.com/search?q=restaurant+CEO&location=houston+tx
2025-08-05 19:48:02,039 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:48:02,047 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:48:02,053 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:48:02,059 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:48:02,066 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:48:02,072 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:48:02,078 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:48:02,094 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.594497105938526
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:48:02,095 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:48:03,773 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 1 for https://www.cyberbackgroundchecks.com/search?q=restaurant+president&location=houston+tx
2025-08-05 19:48:07,435 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:48:07,527 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+president&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:48:07,528 - src.utils.anti_bot - WARNING - Strategy 1 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+president&location=houston+tx
2025-08-05 19:48:07,528 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 2 for https://www.cyberbackgroundchecks.com/search?q=restaurant+president&location=houston+tx
2025-08-05 19:48:15,692 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:48:15,826 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+president&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:48:15,829 - src.utils.anti_bot - WARNING - Strategy 2 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+president&location=houston+tx
2025-08-05 19:48:15,829 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 3 for https://www.cyberbackgroundchecks.com/search?q=restaurant+president&location=houston+tx
2025-08-05 19:48:15,834 - undetected_chromedriver.patcher - DEBUG - getting release number from /last-known-good-versions-with-downloads.json
2025-08-05 19:48:16,051 - undetected_chromedriver.patcher - DEBUG - downloading from https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/138.0.7204.183/mac-x64/chromedriver-mac-x64.zip
2025-08-05 19:48:23,374 - undetected_chromedriver.patcher - DEBUG - unzipping /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmpz7qleoqv
2025-08-05 19:48:23,475 - undetected_chromedriver.patcher - INFO - patching driver executable /Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver
2025-08-05 19:48:23,493 - undetected_chromedriver.patcher - DEBUG - found block:
b'{window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy = window.Proxy;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;window.cdc_adoQpoasnfa76pfcZLmcfl_JSON = window.JSON;window.cdc_adoQpoasnfa76pfcZLmcfl_Window = window.Window;}'
replacing with:
b'{console.log("undetected chromedriver 1337!")}                                                                                                                                                                                                                                                                                                                                                             '
2025-08-05 19:48:23,499 - undetected_chromedriver.patcher - DEBUG - patching took us 0.02 seconds
2025-08-05 19:48:23,518 - selenium.webdriver.common.service - DEBUG - Started executable: `/Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver` in a child process with pid: 11463
2025-08-05 19:48:28,074 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55953/session {"capabilities": {"firstMatch": [{}], "alwaysMatch": {"browserName": "chrome", "pageLoadStrategy": "normal", "goog:chromeOptions": {"extensions": [], "binary": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "args": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-blink-features=AutomationControlled", "--disable-extensions", "--disable-plugins", "--disable-images", "--disable-javascript", "--disable-gpu", "--no-first-run", "--no-default-browser-check", "--disable-default-apps", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/117.0", "--window-size=1320,940", "--remote-debugging-host=127.0.0.1", "--remote-debugging-port=55952", "--user-data-dir=/var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmpwfi4_x0h", "--lang=en-US", "--no-default-browser-check", "--no-first-run", "--no-sandbox", "--test-type", "--window-size=1920,1080", "--start-maximized", "--no-sandbox", "--log-level=0"], "debuggerAddress": "127.0.0.1:55952"}}}}
2025-08-05 19:48:28,075 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:55953
2025-08-05 19:48:28,138 - urllib3.connectionpool - DEBUG - http://localhost:55953 "POST /session HTTP/1.1" 200 0
2025-08-05 19:48:28,138 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.184","chrome":{"chromedriverVersion":"138.0.7204.183 (e90faf484ddbc033fc9bf337621761d3dd5c5289-refs/branch-heads/7204@{#2435})"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"127.0.0.1:55952"},"pageLoadStrategy":"normal","platformName":"","proxy":{},"setWindowRect":false,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"6f800592c00aa380ced756e3cff40839"}} | headers=HTTPHeaderDict({'Content-Length': '762', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:48:28,139 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:28,139 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55953/session/6f800592c00aa380ced756e3cff40839/execute/sync {"script": "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})", "args": []}
2025-08-05 19:48:28,146 - urllib3.connectionpool - DEBUG - http://localhost:55953 "POST /session/6f800592c00aa380ced756e3cff40839/execute/sync HTTP/1.1" 200 0
2025-08-05 19:48:28,146 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:48:28,146 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:28,146 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55953/session/6f800592c00aa380ced756e3cff40839/execute/sync {"script": "\n            Object.defineProperty(navigator, 'plugins', {\n                get: () => [1, 2, 3, 4, 5]\n            });\n            ", "args": []}
2025-08-05 19:48:28,149 - urllib3.connectionpool - DEBUG - http://localhost:55953 "POST /session/6f800592c00aa380ced756e3cff40839/execute/sync HTTP/1.1" 200 0
2025-08-05 19:48:28,149 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:48:28,149 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:28,149 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55953/session/6f800592c00aa380ced756e3cff40839/execute/sync {"script": "\n            Object.defineProperty(navigator, 'languages', {\n                get: () => ['en-US', 'en']\n            });\n            ", "args": []}
2025-08-05 19:48:28,152 - urllib3.connectionpool - DEBUG - http://localhost:55953 "POST /session/6f800592c00aa380ced756e3cff40839/execute/sync HTTP/1.1" 200 0
2025-08-05 19:48:28,152 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:48:28,152 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:28,153 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55953/session/6f800592c00aa380ced756e3cff40839/execute/sync {"script": "\n            const originalQuery = window.navigator.permissions.query;\n            return window.navigator.permissions.query = (parameters) => (\n                parameters.name === 'notifications' ?\n                    Promise.resolve({ state: Notification.permission }) :\n                    originalQuery(parameters)\n            );\n            ", "args": []}
2025-08-05 19:48:28,155 - urllib3.connectionpool - DEBUG - http://localhost:55953 "POST /session/6f800592c00aa380ced756e3cff40839/execute/sync HTTP/1.1" 200 0
2025-08-05 19:48:28,155 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{}} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:48:28,156 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:28,156 - src.utils.anti_bot - INFO - Using Selenium to bypass Cloudflare for https://www.cyberbackgroundchecks.com/search?q=restaurant+president&location=houston+tx
2025-08-05 19:48:28,156 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:55953/session/6f800592c00aa380ced756e3cff40839/url {"url": "https://www.cyberbackgroundchecks.com/search?q=restaurant+president&location=houston+tx"}
2025-08-05 19:48:28,708 - urllib3.connectionpool - DEBUG - http://localhost:55953 "POST /session/6f800592c00aa380ced756e3cff40839/url HTTP/1.1" 200 0
2025-08-05 19:48:28,736 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:48:28,736 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:28,737 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:55953/session/6f800592c00aa380ced756e3cff40839/source {}
2025-08-05 19:48:29,220 - urllib3.connectionpool - DEBUG - http://localhost:55953 "GET /session/6f800592c00aa380ced756e3cff40839/source HTTP/1.1" 200 0
2025-08-05 19:48:29,220 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6ec86ac62b550\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6ec86ac62b550',t:'MTc1NDQwMzUwOC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:48:29,222 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:33,640 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:55953/session/6f800592c00aa380ced756e3cff40839/source {}
2025-08-05 19:48:33,659 - urllib3.connectionpool - DEBUG - http://localhost:55953 "GET /session/6f800592c00aa380ced756e3cff40839/source HTTP/1.1" 200 0
2025-08-05 19:48:33,659 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6ec86ac62b550\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6ec86ac62b550',t:'MTc1NDQwMzUwOC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:48:33,660 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:48:33,661 - src.utils.anti_bot - INFO - Successfully bypassed Cloudflare with Selenium for https://www.cyberbackgroundchecks.com/search?q=restaurant+president&location=houston+tx
2025-08-05 19:48:33,744 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:48:33,751 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:48:33,758 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:48:33,765 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:48:33,772 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:48:33,777 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:48:33,784 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:48:33,799 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.5939864132638543
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:48:33,800 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:48:33,800 - src.utils.anti_bot - INFO - Cloudflare bypass successful for https://www.cyberbackgroundchecks.com/search?q=restaurant+president&location=houston+tx
2025-08-05 19:48:33,843 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:48:33,849 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:48:33,855 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:48:33,861 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:48:33,867 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:48:33,873 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:48:33,879 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:48:33,893 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:48:33,894 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.5939864132638543
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:48:33,895 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:48:35,210 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 1 for https://www.cyberbackgroundchecks.com/search?q=restaurant+founder&location=houston+tx
2025-08-05 19:48:37,841 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:48:37,980 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+founder&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:48:37,982 - src.utils.anti_bot - WARNING - Strategy 1 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+founder&location=houston+tx
2025-08-05 19:48:37,983 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 2 for https://www.cyberbackgroundchecks.com/search?q=restaurant+founder&location=houston+tx
2025-08-05 19:48:46,469 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:48:46,584 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+founder&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:48:46,586 - src.utils.anti_bot - WARNING - Strategy 2 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+founder&location=houston+tx
2025-08-05 19:48:46,586 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 3 for https://www.cyberbackgroundchecks.com/search?q=restaurant+founder&location=houston+tx
2025-08-05 19:48:46,591 - undetected_chromedriver.patcher - DEBUG - getting release number from /last-known-good-versions-with-downloads.json
2025-08-05 19:48:46,725 - undetected_chromedriver.patcher - DEBUG - downloading from https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/138.0.7204.183/mac-x64/chromedriver-mac-x64.zip
2025-08-05 19:48:53,704 - undetected_chromedriver.patcher - DEBUG - unzipping /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmpn4gpzgqf
2025-08-05 19:48:53,808 - undetected_chromedriver.patcher - INFO - patching driver executable /Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver
2025-08-05 19:48:53,828 - undetected_chromedriver.patcher - DEBUG - found block:
b'{window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy = window.Proxy;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;window.cdc_adoQpoasnfa76pfcZLmcfl_JSON = window.JSON;window.cdc_adoQpoasnfa76pfcZLmcfl_Window = window.Window;}'
replacing with:
b'{console.log("undetected chromedriver 1337!")}                                                                                                                                                                                                                                                                                                                                                             '
2025-08-05 19:48:53,836 - undetected_chromedriver.patcher - DEBUG - patching took us 0.03 seconds
2025-08-05 19:48:53,856 - selenium.webdriver.common.service - DEBUG - Started executable: `/Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver` in a child process with pid: 11541
2025-08-05 19:49:00,941 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56134/session {"capabilities": {"firstMatch": [{}], "alwaysMatch": {"browserName": "chrome", "pageLoadStrategy": "normal", "goog:chromeOptions": {"extensions": [], "binary": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "args": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-blink-features=AutomationControlled", "--disable-extensions", "--disable-plugins", "--disable-images", "--disable-javascript", "--disable-gpu", "--no-first-run", "--no-default-browser-check", "--disable-default-apps", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.76", "--window-size=1528,1057", "--remote-debugging-host=127.0.0.1", "--remote-debugging-port=56133", "--user-data-dir=/var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmpkqx3o7ht", "--lang=en-US", "--no-default-browser-check", "--no-first-run", "--no-sandbox", "--test-type", "--window-size=1920,1080", "--start-maximized", "--no-sandbox", "--log-level=0"], "debuggerAddress": "127.0.0.1:56133"}}}}
2025-08-05 19:49:00,944 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:56134
2025-08-05 19:49:01,006 - urllib3.connectionpool - DEBUG - http://localhost:56134 "POST /session HTTP/1.1" 200 0
2025-08-05 19:49:01,006 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.184","chrome":{"chromedriverVersion":"138.0.7204.183 (e90faf484ddbc033fc9bf337621761d3dd5c5289-refs/branch-heads/7204@{#2435})"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"127.0.0.1:56133"},"pageLoadStrategy":"normal","platformName":"","proxy":{},"setWindowRect":false,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"bb155b55f3ac304fc19bc17d83cff610"}} | headers=HTTPHeaderDict({'Content-Length': '762', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:01,007 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:01,007 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56134/session/bb155b55f3ac304fc19bc17d83cff610/execute/sync {"script": "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})", "args": []}
2025-08-05 19:49:01,015 - urllib3.connectionpool - DEBUG - http://localhost:56134 "POST /session/bb155b55f3ac304fc19bc17d83cff610/execute/sync HTTP/1.1" 200 0
2025-08-05 19:49:01,015 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:01,015 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:01,015 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56134/session/bb155b55f3ac304fc19bc17d83cff610/execute/sync {"script": "\n            Object.defineProperty(navigator, 'plugins', {\n                get: () => [1, 2, 3, 4, 5]\n            });\n            ", "args": []}
2025-08-05 19:49:01,019 - urllib3.connectionpool - DEBUG - http://localhost:56134 "POST /session/bb155b55f3ac304fc19bc17d83cff610/execute/sync HTTP/1.1" 200 0
2025-08-05 19:49:01,019 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:01,019 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:01,019 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56134/session/bb155b55f3ac304fc19bc17d83cff610/execute/sync {"script": "\n            Object.defineProperty(navigator, 'languages', {\n                get: () => ['en-US', 'en']\n            });\n            ", "args": []}
2025-08-05 19:49:01,023 - urllib3.connectionpool - DEBUG - http://localhost:56134 "POST /session/bb155b55f3ac304fc19bc17d83cff610/execute/sync HTTP/1.1" 200 0
2025-08-05 19:49:01,023 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:01,023 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:01,023 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56134/session/bb155b55f3ac304fc19bc17d83cff610/execute/sync {"script": "\n            const originalQuery = window.navigator.permissions.query;\n            return window.navigator.permissions.query = (parameters) => (\n                parameters.name === 'notifications' ?\n                    Promise.resolve({ state: Notification.permission }) :\n                    originalQuery(parameters)\n            );\n            ", "args": []}
2025-08-05 19:49:01,028 - urllib3.connectionpool - DEBUG - http://localhost:56134 "POST /session/bb155b55f3ac304fc19bc17d83cff610/execute/sync HTTP/1.1" 200 0
2025-08-05 19:49:01,028 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{}} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:01,028 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:01,028 - src.utils.anti_bot - INFO - Using Selenium to bypass Cloudflare for https://www.cyberbackgroundchecks.com/search?q=restaurant+founder&location=houston+tx
2025-08-05 19:49:01,028 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56134/session/bb155b55f3ac304fc19bc17d83cff610/url {"url": "https://www.cyberbackgroundchecks.com/search?q=restaurant+founder&location=houston+tx"}
2025-08-05 19:49:01,803 - urllib3.connectionpool - DEBUG - http://localhost:56134 "POST /session/bb155b55f3ac304fc19bc17d83cff610/url HTTP/1.1" 200 0
2025-08-05 19:49:01,803 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:01,804 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:01,804 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:56134/session/bb155b55f3ac304fc19bc17d83cff610/source {}
2025-08-05 19:49:01,807 - urllib3.connectionpool - DEBUG - http://localhost:56134 "GET /session/bb155b55f3ac304fc19bc17d83cff610/source HTTP/1.1" 200 0
2025-08-05 19:49:01,807 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6ed543c734433\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6ed543c734433',t:'MTc1NDQwMzU0MS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:01,808 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:07,564 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:56134/session/bb155b55f3ac304fc19bc17d83cff610/source {}
2025-08-05 19:49:07,568 - urllib3.connectionpool - DEBUG - http://localhost:56134 "GET /session/bb155b55f3ac304fc19bc17d83cff610/source HTTP/1.1" 200 0
2025-08-05 19:49:07,568 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6ed543c734433\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6ed543c734433',t:'MTc1NDQwMzU0MS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:07,568 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:07,569 - src.utils.anti_bot - INFO - Successfully bypassed Cloudflare with Selenium for https://www.cyberbackgroundchecks.com/search?q=restaurant+founder&location=houston+tx
2025-08-05 19:49:07,640 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:49:07,649 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:49:07,673 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:49:07,683 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:49:07,690 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:49:07,698 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:49:07,706 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:49:07,723 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:49:07,723 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:49:07,724 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:49:07,724 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:49:07,724 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:49:07,724 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:49:07,724 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:49:07,724 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:49:07,724 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:49:07,725 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.5971052174749341
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:49:07,726 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:49:07,726 - src.utils.anti_bot - INFO - Cloudflare bypass successful for https://www.cyberbackgroundchecks.com/search?q=restaurant+founder&location=houston+tx
2025-08-05 19:49:07,769 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:49:07,775 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:49:07,781 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:49:07,787 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:49:07,794 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:49:07,800 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:49:07,807 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:49:07,824 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:49:07,825 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.5971052174749341
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:49:07,826 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:49:10,795 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 1 for https://www.cyberbackgroundchecks.com/search?q=restaurant+business+owner&location=houston+tx
2025-08-05 19:49:14,411 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:49:14,523 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+business+owner&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:49:14,525 - src.utils.anti_bot - WARNING - Strategy 1 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+business+owner&location=houston+tx
2025-08-05 19:49:14,525 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 2 for https://www.cyberbackgroundchecks.com/search?q=restaurant+business+owner&location=houston+tx
2025-08-05 19:49:20,683 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.cyberbackgroundchecks.com:443
2025-08-05 19:49:20,792 - urllib3.connectionpool - DEBUG - https://www.cyberbackgroundchecks.com:443 "GET /search?q=restaurant+business+owner&location=houston+tx HTTP/1.1" 403 None
2025-08-05 19:49:20,794 - src.utils.anti_bot - WARNING - Strategy 2 failed for https://www.cyberbackgroundchecks.com/search?q=restaurant+business+owner&location=houston+tx
2025-08-05 19:49:20,794 - src.utils.anti_bot - INFO - Cloudflare bypass attempt 1, strategy 3 for https://www.cyberbackgroundchecks.com/search?q=restaurant+business+owner&location=houston+tx
2025-08-05 19:49:20,798 - undetected_chromedriver.patcher - DEBUG - getting release number from /last-known-good-versions-with-downloads.json
2025-08-05 19:49:20,978 - undetected_chromedriver.patcher - DEBUG - downloading from https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/138.0.7204.183/mac-x64/chromedriver-mac-x64.zip
2025-08-05 19:49:25,269 - undetected_chromedriver.patcher - DEBUG - unzipping /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmpxgv8fkt8
2025-08-05 19:49:25,393 - undetected_chromedriver.patcher - INFO - patching driver executable /Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver
2025-08-05 19:49:25,413 - undetected_chromedriver.patcher - DEBUG - found block:
b'{window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy = window.Proxy;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;window.cdc_adoQpoasnfa76pfcZLmcfl_JSON = window.JSON;window.cdc_adoQpoasnfa76pfcZLmcfl_Window = window.Window;}'
replacing with:
b'{console.log("undetected chromedriver 1337!")}                                                                                                                                                                                                                                                                                                                                                             '
2025-08-05 19:49:25,420 - undetected_chromedriver.patcher - DEBUG - patching took us 0.03 seconds
2025-08-05 19:49:25,439 - selenium.webdriver.common.service - DEBUG - Started executable: `/Users/<USER>/Library/Application Support/undetected_chromedriver/undetected_chromedriver` in a child process with pid: 11672
2025-08-05 19:49:31,009 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56361/session {"capabilities": {"firstMatch": [{}], "alwaysMatch": {"browserName": "chrome", "pageLoadStrategy": "normal", "goog:chromeOptions": {"extensions": [], "binary": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "args": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-blink-features=AutomationControlled", "--disable-extensions", "--disable-plugins", "--disable-images", "--disable-javascript", "--disable-gpu", "--no-first-run", "--no-default-browser-check", "--disable-default-apps", "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15", "--window-size=1204,1077", "--remote-debugging-host=127.0.0.1", "--remote-debugging-port=56360", "--user-data-dir=/var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/T/tmp9_8dp9uz", "--lang=en-US", "--no-default-browser-check", "--no-first-run", "--no-sandbox", "--test-type", "--window-size=1920,1080", "--start-maximized", "--no-sandbox", "--log-level=0"], "debuggerAddress": "127.0.0.1:56360"}}}}
2025-08-05 19:49:31,011 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:56361
2025-08-05 19:49:31,084 - urllib3.connectionpool - DEBUG - http://localhost:56361 "POST /session HTTP/1.1" 200 0
2025-08-05 19:49:31,085 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.184","chrome":{"chromedriverVersion":"138.0.7204.183 (e90faf484ddbc033fc9bf337621761d3dd5c5289-refs/branch-heads/7204@{#2435})"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"127.0.0.1:56360"},"pageLoadStrategy":"normal","platformName":"","proxy":{},"setWindowRect":false,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"56562d795fde9ce09dde3e79daa162d1"}} | headers=HTTPHeaderDict({'Content-Length': '762', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:31,085 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:31,085 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56361/session/56562d795fde9ce09dde3e79daa162d1/execute/sync {"script": "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})", "args": []}
2025-08-05 19:49:31,095 - urllib3.connectionpool - DEBUG - http://localhost:56361 "POST /session/56562d795fde9ce09dde3e79daa162d1/execute/sync HTTP/1.1" 200 0
2025-08-05 19:49:31,095 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:31,095 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:31,095 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56361/session/56562d795fde9ce09dde3e79daa162d1/execute/sync {"script": "\n            Object.defineProperty(navigator, 'plugins', {\n                get: () => [1, 2, 3, 4, 5]\n            });\n            ", "args": []}
2025-08-05 19:49:31,098 - urllib3.connectionpool - DEBUG - http://localhost:56361 "POST /session/56562d795fde9ce09dde3e79daa162d1/execute/sync HTTP/1.1" 200 0
2025-08-05 19:49:31,098 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:31,098 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:31,099 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56361/session/56562d795fde9ce09dde3e79daa162d1/execute/sync {"script": "\n            Object.defineProperty(navigator, 'languages', {\n                get: () => ['en-US', 'en']\n            });\n            ", "args": []}
2025-08-05 19:49:31,102 - urllib3.connectionpool - DEBUG - http://localhost:56361 "POST /session/56562d795fde9ce09dde3e79daa162d1/execute/sync HTTP/1.1" 200 0
2025-08-05 19:49:31,102 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:31,102 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:31,102 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56361/session/56562d795fde9ce09dde3e79daa162d1/execute/sync {"script": "\n            const originalQuery = window.navigator.permissions.query;\n            return window.navigator.permissions.query = (parameters) => (\n                parameters.name === 'notifications' ?\n                    Promise.resolve({ state: Notification.permission }) :\n                    originalQuery(parameters)\n            );\n            ", "args": []}
2025-08-05 19:49:31,106 - urllib3.connectionpool - DEBUG - http://localhost:56361 "POST /session/56562d795fde9ce09dde3e79daa162d1/execute/sync HTTP/1.1" 200 0
2025-08-05 19:49:31,106 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{}} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:31,106 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:31,106 - src.utils.anti_bot - INFO - Using Selenium to bypass Cloudflare for https://www.cyberbackgroundchecks.com/search?q=restaurant+business+owner&location=houston+tx
2025-08-05 19:49:31,106 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:56361/session/56562d795fde9ce09dde3e79daa162d1/url {"url": "https://www.cyberbackgroundchecks.com/search?q=restaurant+business+owner&location=houston+tx"}
2025-08-05 19:49:31,355 - urllib3.connectionpool - DEBUG - http://localhost:56361 "POST /session/56562d795fde9ce09dde3e79daa162d1/url HTTP/1.1" 200 0
2025-08-05 19:49:31,356 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:31,356 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:31,356 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:56361/session/56562d795fde9ce09dde3e79daa162d1/source {}
2025-08-05 19:49:31,360 - urllib3.connectionpool - DEBUG - http://localhost:56361 "GET /session/56562d795fde9ce09dde3e79daa162d1/source HTTP/1.1" 200 0
2025-08-05 19:49:31,360 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6ee1049ea47b4\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6ee1049ea47b4',t:'MTc1NDQwMzU3MS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:31,361 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:35,100 - selenium.webdriver.remote.remote_connection - DEBUG - GET http://localhost:56361/session/56562d795fde9ce09dde3e79daa162d1/source {}
2025-08-05 19:49:35,104 - urllib3.connectionpool - DEBUG - http://localhost:56361 "GET /session/56562d795fde9ce09dde3e79daa162d1/source HTTP/1.1" 200 0
2025-08-05 19:49:35,104 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\"no-js\" lang=\"en-US\">\u003C!--\u003C![endif]-->\u003Chead>\n\u003Ctitle>Attention Required! | Cloudflare\u003C/title>\n\u003Cmeta charset=\"UTF-8\">\n\u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\u003Cmeta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\">\n\u003Cmeta name=\"robots\" content=\"noindex, nofollow\">\n\u003Cmeta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n\u003Clink rel=\"stylesheet\" id=\"cf_styles-css\" href=\"/cdn-cgi/styles/cf.errors.css\">\n\u003C!--[if lt IE 9]>\u003Clink rel=\"stylesheet\" id='cf_styles-ie-css' href=\"/cdn-cgi/styles/cf.errors.ie.css\" />\u003C![endif]-->\n\u003Cstyle>body{margin:0;padding:0}\u003C/style>\n\n\n\u003C!--[if gte IE 10]>\u003C!-->\n\u003Cscript>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n\u003C/script>\n\u003C!--\u003C![endif]-->\n\n\n\u003C/head>\n\u003Cbody>\n  \u003Cdiv id=\"cf-wrapper\">\n    \u003Cdiv class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.\u003C/div>\n    \u003Cdiv id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      \u003Cdiv class=\"cf-wrapper cf-header cf-error-overview\">\n        \u003Ch1 data-translate=\"block_headline\">Sorry, you have been blocked\u003C/h1>\n        \u003Ch2 class=\"cf-subheadline\">\u003Cspan data-translate=\"unable_to_access\">You are unable to access\u003C/span> cyberbackgroundchecks.com\u003C/h2>\n      \u003C/div>\u003C!-- /.header -->\n\n      \u003Cdiv class=\"cf-section cf-highlight\">\n        \u003Cdiv class=\"cf-wrapper\">\n          \u003Cdiv class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              \u003Cspan class=\"cf-no-screenshot error\">\u003C/span>\n            \n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.captcha-container -->\n\n      \u003Cdiv class=\"cf-section cf-wrapper\">\n        \u003Cdiv class=\"cf-columns two\">\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_why_headline\">Why have I been blocked?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.\u003C/p>\n          \u003C/div>\n\n          \u003Cdiv class=\"cf-column\">\n            \u003Ch2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?\u003C/h2>\n\n            \u003Cp data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.\u003C/p>\n          \u003C/div>\n        \u003C/div>\n      \u003C/div>\u003C!-- /.section -->\n\n      \u003Cdiv class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n  \u003Cp class=\"text-13\">\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: \u003Cstrong class=\"font-semibold\">96a6ee1049ea47b4\u003C/strong>\u003C/span>\n    \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003Cspan id=\"cf-footer-item-ip\" class=\"cf-footer-item sm:block sm:mb-1\">\n      Your IP:\n      \u003Cbutton type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal\u003C/button>\n      \u003Cspan class=\"hidden\" id=\"cf-footer-ip\">**************\u003C/span>\n      \u003Cspan class=\"cf-footer-separator sm:hidden\">•\u003C/span>\n    \u003C/span>\n    \u003Cspan class=\"cf-footer-item sm:block sm:mb-1\">\u003Cspan>Performance &amp; security by\u003C/span> \u003Ca rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare\u003C/a>\u003C/span>\n    \n  \u003C/p>\n  \u003Cscript>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();\u003C/script>\n\u003C/div>\u003C!-- /.error-footer -->\n\n\n    \u003C/div>\u003C!-- /#cf-error-details -->\n  \u003C/div>\u003C!-- /#cf-wrapper -->\n\n  \u003Cscript>\n  window._cf_translation = {};\n  \n  \n\u003C/script>\n\n\u003Cscript>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'96a6ee1049ea47b4',t:'MTc1NDQwMzU3MS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();\u003C/script>\u003Ciframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\">\u003C/iframe>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '6050', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-08-05 19:49:35,105 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-08-05 19:49:35,105 - src.utils.anti_bot - INFO - Successfully bypassed Cloudflare with Selenium for https://www.cyberbackgroundchecks.com/search?q=restaurant+business+owner&location=houston+tx
2025-08-05 19:49:35,188 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:49:35,197 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:49:35,203 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:49:35,211 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:49:35,218 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:49:35,225 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:49:35,232 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:49:35,248 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.59663077162325
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:49:35,249 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:49:35,250 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:49:35,250 - src.utils.anti_bot - INFO - Cloudflare bypass successful for https://www.cyberbackgroundchecks.com/search?q=restaurant+business+owner&location=houston+tx
2025-08-05 19:49:35,293 - chardet.charsetprober - DEBUG - EUC-JP Japanese prober hit error at byte 3078
2025-08-05 19:49:35,300 - chardet.charsetprober - DEBUG - GB2312 Chinese prober hit error at byte 3080
2025-08-05 19:49:35,308 - chardet.charsetprober - DEBUG - EUC-KR Korean prober hit error at byte 3078
2025-08-05 19:49:35,316 - chardet.charsetprober - DEBUG - CP949 Korean prober hit error at byte 3078
2025-08-05 19:49:35,325 - chardet.charsetprober - DEBUG - Big5 Chinese prober hit error at byte 3080
2025-08-05 19:49:35,334 - chardet.charsetprober - DEBUG - EUC-TW Taiwan prober hit error at byte 3078
2025-08-05 19:49:35,342 - chardet.charsetprober - DEBUG - Johab Korean prober hit error at byte 3078
2025-08-05 19:49:35,362 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - windows-1251 Russian confidence = 0.01
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - KOI8-R Russian confidence = 0.01
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - ISO-8859-5 Russian confidence = 0.01
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - MacCyrillic Russian confidence = 0.0
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - IBM866 Russian confidence = 0.12799495392693638
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - IBM855 Russian confidence = 0.01
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - ISO-8859-7 Greek confidence = 0.01
2025-08-05 19:49:35,363 - chardet.charsetprober - DEBUG - windows-1253 Greek confidence = 0.01
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - ISO-8859-5 Bulgarian confidence = 0.01
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - windows-1251 Bulgarian confidence = 0.01
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - TIS-620 Thai confidence = 0.01
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - ISO-8859-9 Turkish confidence = 0.59663077162325
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.0
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - windows-1255 Hebrew confidence = 0.01
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - utf-8  confidence = 0.7525
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - SHIFT_JIS Japanese confidence = 0.01
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - EUC-JP not active
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - GB2312 not active
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - EUC-KR not active
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - CP949 not active
2025-08-05 19:49:35,364 - chardet.charsetprober - DEBUG - Big5 not active
2025-08-05 19:49:35,365 - chardet.charsetprober - DEBUG - EUC-TW not active
2025-08-05 19:49:35,365 - chardet.charsetprober - DEBUG - Johab not active
2025-08-05 19:49:37,852 - __main__ - INFO - Found 0 results from cyberbackgroundchecks
2025-08-05 19:49:37,853 - __main__ - INFO - [4/4] Scraping truthfinder_api for 'restaurant' in 'houston tx'
2025-08-05 19:49:37,853 - src.scrapers.truthfinder_scraper - INFO - Searching TruthFinder API for restaurant owners in houston tx
2025-08-05 19:49:37,853 - src.apis.truthfinder_api - INFO - Searching TruthFinder API for restaurant owners in Houston, TX
2025-08-05 19:49:37,858 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.truthfinder.com:443
2025-08-05 19:49:37,973 - urllib3.connectionpool - DEBUG - https://api.truthfinder.com:443 "POST /search HTTP/1.1" 403 None
2025-08-05 19:49:37,974 - src.apis.truthfinder_api - WARNING - API request failed with status 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->


</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> truthfinder.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
  <p class="text-13">
    <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">96a6ee3a3ac1ffa5</strong></span>
    <span class="cf-footer-separator sm:hidden">&bull;</span>
    <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
      Your IP:
      <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
      <span class="hidden" id="cf-footer-ip">**************</span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
    </span>
    <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
    
  </p>
  <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
</div><!-- /.error-footer -->


    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
  window._cf_translation = {};
  
  
</script>

</body>
</html>

2025-08-05 19:49:37,977 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.truthfinder.com:443
2025-08-05 19:49:38,060 - urllib3.connectionpool - DEBUG - https://api.truthfinder.com:443 "POST /search HTTP/1.1" 403 None
2025-08-05 19:49:38,062 - src.apis.truthfinder_api - WARNING - API request failed with status 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->


</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> truthfinder.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
  <p class="text-13">
    <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">96a6ee3acfe6ff6a</strong></span>
    <span class="cf-footer-separator sm:hidden">&bull;</span>
    <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
      Your IP:
      <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
      <span class="hidden" id="cf-footer-ip">**************</span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
    </span>
    <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
    
  </p>
  <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
</div><!-- /.error-footer -->


    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
  window._cf_translation = {};
  
  
</script>

</body>
</html>

2025-08-05 19:49:38,064 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.truthfinder.com:443
2025-08-05 19:49:38,166 - urllib3.connectionpool - DEBUG - https://api.truthfinder.com:443 "POST /search HTTP/1.1" 403 None
2025-08-05 19:49:38,168 - src.apis.truthfinder_api - WARNING - API request failed with status 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->


</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> truthfinder.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
  <p class="text-13">
    <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">96a6ee3b68351da3</strong></span>
    <span class="cf-footer-separator sm:hidden">&bull;</span>
    <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
      Your IP:
      <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
      <span class="hidden" id="cf-footer-ip">**************</span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
    </span>
    <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
    
  </p>
  <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
</div><!-- /.error-footer -->


    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
  window._cf_translation = {};
  
  
</script>

</body>
</html>

2025-08-05 19:49:38,168 - src.apis.truthfinder_api - INFO - TruthFinder API returned 0 results
2025-08-05 19:49:38,170 - src.scrapers.truthfinder_scraper - INFO - TruthFinder API returned 0 valid results
2025-08-05 19:49:38,170 - __main__ - INFO - Found 0 results from truthfinder_api
2025-08-05 19:49:38,170 - __main__ - INFO - Raw scraping complete: 0 total results
2025-08-05 19:49:43,172 - src.utils.monitoring - INFO - Scraper monitoring stopped
2025-08-05 19:49:43,172 - __main__ - INFO - Monitoring system stopped
2025-08-05 19:50:17,985 - src.utils.monitoring - INFO - Scraper monitoring system initialized
2025-08-05 19:50:17,985 - src.utils.fallback_system - DEBUG - Loaded fallback rule for bbb
2025-08-05 19:50:17,985 - src.utils.fallback_system - DEBUG - Loaded fallback rule for manta
2025-08-05 19:50:17,985 - src.utils.fallback_system - DEBUG - Loaded fallback rule for linkedin
2025-08-05 19:50:17,985 - src.utils.fallback_system - DEBUG - Loaded fallback rule for truepeoplesearch
2025-08-05 19:50:17,985 - src.utils.fallback_system - DEBUG - Loaded fallback rule for bbb
2025-08-05 19:50:17,985 - src.utils.fallback_system - DEBUG - Loaded fallback rule for manta
2025-08-05 19:50:17,985 - src.utils.fallback_system - INFO - Fallback system initialized
2025-08-05 19:50:17,985 - src.utils.fallback_system - INFO - Health checker initialized
2025-08-05 19:50:17,985 - src.utils.fallback_system - INFO - Alert system initialized
2025-08-05 19:50:17,985 - src.utils.monitoring - INFO - Scraper monitoring started
2025-08-05 19:50:17,985 - __main__ - INFO - Monitoring system started
2025-08-05 19:50:17,987 - src.utils.google_custom_search - WARNING - Google Custom Search API key not provided
2025-08-05 19:50:17,987 - src.utils.google_custom_search - WARNING - Google Custom Search Engine ID not provided
2025-08-05 19:50:17,987 - src.utils.google_custom_search - WARNING - Google Custom Search API is disabled
2025-08-05 19:50:17,987 - src.utils.google_custom_search - INFO - Google search fallback disabled
2025-08-05 19:50:17,987 - src.utils.google_search - WARNING - Google Custom Search API not available - using fallback
2025-08-05 19:50:17,988 - src.utils.google_search - WARNING - For better results, configure Google Custom Search API
2025-08-05 19:50:17,988 - src.scrapers.base_scraper.bbb - INFO - Enhanced Cloudflare bypass enabled for BBB
2025-08-05 19:50:17,988 - src.utils.google_custom_search - WARNING - Google Custom Search API key not provided
2025-08-05 19:50:17,988 - src.utils.google_custom_search - WARNING - Google Custom Search Engine ID not provided
2025-08-05 19:50:17,988 - src.utils.google_custom_search - WARNING - Google Custom Search API is disabled
2025-08-05 19:50:17,988 - src.utils.google_custom_search - INFO - Google search fallback disabled
2025-08-05 19:50:17,988 - src.utils.google_search - WARNING - Google Custom Search API not available - using fallback
2025-08-05 19:50:17,988 - src.utils.google_search - WARNING - For better results, configure Google Custom Search API
2025-08-05 19:50:17,988 - src.scrapers.base_scraper.manta - INFO - Enhanced Cloudflare bypass enabled for Manta
2025-08-05 19:50:17,990 - src.scrapers.truthfinder_scraper - INFO - TruthFinder API scraper initialized successfully
2025-08-05 19:50:17,990 - __main__ - INFO - LinkedIn scraper disabled (recommended for production)
2025-08-05 19:50:17,990 - src.utils.monitoring - INFO - Registered scraper for monitoring: bbb
2025-08-05 19:50:17,990 - src.utils.fallback_system - DEBUG - Registered health check: bbb_health
2025-08-05 19:50:17,991 - src.utils.monitoring - INFO - Registered scraper for monitoring: manta
2025-08-05 19:50:17,991 - src.utils.fallback_system - DEBUG - Registered health check: manta_health
2025-08-05 19:50:17,991 - src.utils.monitoring - INFO - Registered scraper for monitoring: truepeoplesearch
2025-08-05 19:50:17,991 - src.utils.fallback_system - DEBUG - Registered health check: truepeoplesearch_health
2025-08-05 19:50:17,991 - src.utils.monitoring - INFO - Registered scraper for monitoring: cyberbackgroundchecks
2025-08-05 19:50:17,991 - src.utils.fallback_system - DEBUG - Registered health check: cyberbackgroundchecks_health
2025-08-05 19:50:17,991 - src.utils.monitoring - INFO - Registered scraper for monitoring: truthfinder_api
2025-08-05 19:50:17,991 - src.utils.fallback_system - DEBUG - Registered health check: truthfinder_api_health
2025-08-05 19:50:17,991 - __main__ - INFO - Starting scrape for 1 business types, 1 locations, using 1 sources
2025-08-05 19:50:17,991 - __main__ - INFO - [1/1] Scraping bbb for 'lawn care' in 'dallas tx'
2025-08-05 19:50:17,991 - src.scrapers.base_scraper.bbb - INFO - Searching BBB via Google for 'lawn care' owners in 'dallas tx'
2025-08-05 19:50:17,991 - src.scrapers.base_scraper.bbb - INFO - Found 0 BBB listings from Google search
2025-08-05 19:50:17,991 - __main__ - INFO - Found 0 results from bbb
2025-08-05 19:50:17,991 - __main__ - INFO - Raw scraping complete: 0 total results
2025-08-05 19:50:22,996 - src.utils.monitoring - INFO - Scraper monitoring stopped
2025-08-05 19:50:22,996 - __main__ - INFO - Monitoring system stopped
2025-08-05 19:50:32,045 - src.utils.monitoring - INFO - Scraper monitoring system initialized
2025-08-05 19:50:32,045 - src.utils.fallback_system - DEBUG - Loaded fallback rule for bbb
2025-08-05 19:50:32,045 - src.utils.fallback_system - DEBUG - Loaded fallback rule for manta
2025-08-05 19:50:32,045 - src.utils.fallback_system - DEBUG - Loaded fallback rule for linkedin
2025-08-05 19:50:32,045 - src.utils.fallback_system - DEBUG - Loaded fallback rule for truepeoplesearch
2025-08-05 19:50:32,045 - src.utils.fallback_system - DEBUG - Loaded fallback rule for bbb
2025-08-05 19:50:32,045 - src.utils.fallback_system - DEBUG - Loaded fallback rule for manta
2025-08-05 19:50:32,045 - src.utils.fallback_system - INFO - Fallback system initialized
2025-08-05 19:50:32,045 - src.utils.fallback_system - INFO - Health checker initialized
2025-08-05 19:50:32,045 - src.utils.fallback_system - INFO - Alert system initialized
2025-08-05 19:50:32,046 - src.utils.monitoring - INFO - Scraper monitoring started
2025-08-05 19:50:32,046 - __main__ - INFO - Monitoring system started
2025-08-05 19:50:32,052 - src.utils.google_custom_search - WARNING - Google Custom Search API key not provided
2025-08-05 19:50:32,052 - src.utils.google_custom_search - WARNING - Google Custom Search Engine ID not provided
2025-08-05 19:50:32,052 - src.utils.google_custom_search - WARNING - Google Custom Search API is disabled
2025-08-05 19:50:32,053 - src.utils.google_custom_search - INFO - Google search fallback disabled
2025-08-05 19:50:32,053 - src.utils.google_search - WARNING - Google Custom Search API not available - using fallback
2025-08-05 19:50:32,053 - src.utils.google_search - WARNING - For better results, configure Google Custom Search API
2025-08-05 19:50:32,053 - src.scrapers.base_scraper.bbb - INFO - Enhanced Cloudflare bypass enabled for BBB
2025-08-05 19:50:32,055 - src.utils.google_custom_search - WARNING - Google Custom Search API key not provided
2025-08-05 19:50:32,055 - src.utils.google_custom_search - WARNING - Google Custom Search Engine ID not provided
2025-08-05 19:50:32,055 - src.utils.google_custom_search - WARNING - Google Custom Search API is disabled
2025-08-05 19:50:32,055 - src.utils.google_custom_search - INFO - Google search fallback disabled
2025-08-05 19:50:32,055 - src.utils.google_search - WARNING - Google Custom Search API not available - using fallback
2025-08-05 19:50:32,055 - src.utils.google_search - WARNING - For better results, configure Google Custom Search API
2025-08-05 19:50:32,055 - src.scrapers.base_scraper.manta - INFO - Enhanced Cloudflare bypass enabled for Manta
2025-08-05 19:50:32,058 - src.scrapers.truthfinder_scraper - INFO - TruthFinder API scraper initialized successfully
2025-08-05 19:50:32,058 - __main__ - INFO - LinkedIn scraper disabled (recommended for production)
2025-08-05 19:50:32,058 - src.utils.monitoring - INFO - Registered scraper for monitoring: bbb
2025-08-05 19:50:32,058 - src.utils.fallback_system - DEBUG - Registered health check: bbb_health
2025-08-05 19:50:32,058 - src.utils.monitoring - INFO - Registered scraper for monitoring: manta
2025-08-05 19:50:32,059 - src.utils.fallback_system - DEBUG - Registered health check: manta_health
2025-08-05 19:50:32,059 - src.utils.monitoring - INFO - Registered scraper for monitoring: truepeoplesearch
2025-08-05 19:50:32,059 - src.utils.fallback_system - DEBUG - Registered health check: truepeoplesearch_health
2025-08-05 19:50:32,059 - src.utils.monitoring - INFO - Registered scraper for monitoring: cyberbackgroundchecks
2025-08-05 19:50:32,059 - src.utils.fallback_system - DEBUG - Registered health check: cyberbackgroundchecks_health
2025-08-05 19:50:32,059 - src.utils.monitoring - INFO - Registered scraper for monitoring: truthfinder_api
2025-08-05 19:50:32,059 - src.utils.fallback_system - DEBUG - Registered health check: truthfinder_api_health
2025-08-05 19:50:32,059 - __main__ - INFO - Starting scrape for 1 business types, 1 locations, using 1 sources
2025-08-05 19:50:32,059 - __main__ - INFO - [1/1] Scraping truthfinder_api for 'restaurant' in 'houston tx'
2025-08-05 19:50:32,059 - src.scrapers.truthfinder_scraper - INFO - Searching TruthFinder API for restaurant owners in houston tx
2025-08-05 19:50:32,059 - src.apis.truthfinder_api - INFO - Searching TruthFinder API for restaurant owners in Houston, TX
2025-08-05 19:50:32,071 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.truthfinder.com:443
2025-08-05 19:50:32,218 - urllib3.connectionpool - DEBUG - https://api.truthfinder.com:443 "POST /search HTTP/1.1" 403 None
2025-08-05 19:50:32,219 - src.apis.truthfinder_api - WARNING - API request failed with status 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->


</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> truthfinder.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
  <p class="text-13">
    <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">96a6ef8cdc4647d0</strong></span>
    <span class="cf-footer-separator sm:hidden">&bull;</span>
    <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
      Your IP:
      <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
      <span class="hidden" id="cf-footer-ip">**************</span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
    </span>
    <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
    
  </p>
  <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
</div><!-- /.error-footer -->


    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
  window._cf_translation = {};
  
  
</script>

</body>
</html>

2025-08-05 19:50:32,222 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.truthfinder.com:443
2025-08-05 19:50:32,320 - urllib3.connectionpool - DEBUG - https://api.truthfinder.com:443 "POST /search HTTP/1.1" 403 None
2025-08-05 19:50:32,322 - src.apis.truthfinder_api - WARNING - API request failed with status 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->


</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> truthfinder.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
  <p class="text-13">
    <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">96a6ef8debef470a</strong></span>
    <span class="cf-footer-separator sm:hidden">&bull;</span>
    <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
      Your IP:
      <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
      <span class="hidden" id="cf-footer-ip">**************</span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
    </span>
    <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
    
  </p>
  <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
</div><!-- /.error-footer -->


    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
  window._cf_translation = {};
  
  
</script>

</body>
</html>

2025-08-05 19:50:32,325 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.truthfinder.com:443
2025-08-05 19:50:32,453 - urllib3.connectionpool - DEBUG - https://api.truthfinder.com:443 "POST /search HTTP/1.1" 403 None
2025-08-05 19:50:32,457 - src.apis.truthfinder_api - WARNING - API request failed with status 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->


</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> truthfinder.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
  <p class="text-13">
    <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">96a6ef8e79691432</strong></span>
    <span class="cf-footer-separator sm:hidden">&bull;</span>
    <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
      Your IP:
      <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
      <span class="hidden" id="cf-footer-ip">**************</span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
    </span>
    <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
    
  </p>
  <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
</div><!-- /.error-footer -->


    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
  window._cf_translation = {};
  
  
</script>

</body>
</html>

2025-08-05 19:50:32,458 - src.apis.truthfinder_api - INFO - TruthFinder API returned 0 results
2025-08-05 19:50:32,461 - src.scrapers.truthfinder_scraper - INFO - TruthFinder API returned 0 valid results
2025-08-05 19:50:32,461 - __main__ - INFO - Found 0 results from truthfinder_api
2025-08-05 19:50:32,461 - __main__ - INFO - Raw scraping complete: 0 total results
2025-08-05 19:50:37,466 - src.utils.monitoring - INFO - Scraper monitoring stopped
2025-08-05 19:50:37,467 - __main__ - INFO - Monitoring system stopped
