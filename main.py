#!/usr/bin/env python3
"""
Business Owner Scraper - Main CLI Interface

A comprehensive web scraping bot that extracts business owner information 
from multiple sources without using APIs.

Usage:
    python main.py --business-type "lawn care" --location "dallas tx"
    python main.py --config custom_config.yaml --output results.xlsx
    python main.py --interactive
"""

import click
import logging
import sys
import os
import logging
from typing import List, Optional
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core import ScrapingEngine, ScrapingResult
from src.scrapers.bbb_scraper import BBBScraper
from src.scrapers.manta_scraper import MantaScraper
from src.scrapers.linkedin_scraper import LinkedInScraper
from src.scrapers.truepeoplesearch_scraper import TruePeopleSearchScraper
from src.scrapers.cyberbackgroundchecks_scraper import CyberBackgroundChecksScraper
from src.utils.data_processor import DataProcessor
from src.exporters.csv_exporter import CSVExporter
from src.exporters.excel_exporter import ExcelExporter


class BusinessOwnerScraper:
    """Main scraper orchestrator."""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.engine = ScrapingEngine(config_path)
        self.data_processor = DataProcessor(self.engine.config)
        self.csv_exporter = CSVExporter(self.engine.config)
        self.excel_exporter = ExcelExporter(self.engine.config)
        self.logger = logging.getLogger(__name__)

        # Initialize monitoring and fallback systems
        from src.utils.monitoring import ScraperMonitor
        from src.utils.fallback_system import FallbackSystem, HealthChecker, AlertSystem

        self.monitor = ScraperMonitor(self.engine.config)
        self.fallback_system = FallbackSystem(self.engine.config)
        self.health_checker = HealthChecker(self.engine.config)
        self.alert_system = AlertSystem(self.engine.config)

        # Start monitoring
        if self.monitor.enabled:
            self.monitor.start_monitoring()
            self.logger.info("Monitoring system started")
        
        # Initialize scrapers
        from src.scrapers.truthfinder_scraper import TruthFinderScraper

        self.scrapers = {
            'bbb': BBBScraper(self.engine),
            'manta': MantaScraper(self.engine),
            'truepeoplesearch': TruePeopleSearchScraper(self.engine),
            'cyberbackgroundchecks': CyberBackgroundChecksScraper(self.engine),
            'truthfinder_api': TruthFinderScraper(self.engine)
        }

        # Conditionally add LinkedIn scraper only if explicitly enabled
        # LinkedIn is disabled by default due to reliability and legal issues
        linkedin_config = self.engine.config.get('sources', {}).get('linkedin', {})
        if linkedin_config.get('enabled', False) and not linkedin_config.get('deprecated', False):
            self.scrapers['linkedin'] = LinkedInScraper(self.engine)
            self.logger.warning("LinkedIn scraper is enabled - this is not recommended for production")
        else:
            self.logger.info("LinkedIn scraper disabled (recommended for production)")

        # Register scrapers with monitoring system
        for name, scraper in self.scrapers.items():
            self.monitor.register_scraper(name, scraper.is_enabled())

            # Register health checks
            self.health_checker.register_health_check(
                f"{name}_health",
                lambda s=scraper: self.health_checker.check_scraper_health(s)
            )
        

    
    def scrape(self, business_types: List[str], locations: List[str], 
               sources: Optional[List[str]] = None) -> List[ScrapingResult]:
        """Scrape business owner information."""
        all_results = []
        
        # Use all enabled scrapers if none specified
        if not sources:
            sources = [name for name, scraper in self.scrapers.items() if scraper.is_enabled()]
        
        self.logger.info(f"Starting scrape for {len(business_types)} business types, "
                        f"{len(locations)} locations, using {len(sources)} sources")
        
        total_combinations = len(business_types) * len(locations) * len(sources)
        current_combination = 0
        
        for business_type in business_types:
            for location in locations:
                for source_name in sources:
                    current_combination += 1
                    
                    if source_name not in self.scrapers:
                        self.logger.warning(f"Unknown scraper: {source_name}")
                        continue
                    
                    scraper = self.scrapers[source_name]
                    
                    self.logger.info(f"[{current_combination}/{total_combinations}] "
                                   f"Scraping {source_name} for '{business_type}' in '{location}'")
                    
                    try:
                        results = scraper.search(business_type, location)
                        all_results.extend(results)
                        
                        self.logger.info(f"Found {len(results)} results from {source_name}")
                        
                    except Exception as e:
                        self.logger.error(f"Error scraping {source_name}: {e}")
                        continue
        
        self.logger.info(f"Raw scraping complete: {len(all_results)} total results")
        return all_results

    def get_monitoring_status(self) -> dict:
        """Get current monitoring status and metrics."""
        if not self.monitor.enabled:
            return {'monitoring': 'disabled'}

        return {
            'system_metrics': self.monitor.get_system_status(),
            'scraper_metrics': {
                name: self.monitor.get_scraper_status(name)
                for name in self.scrapers.keys()
            },
            'fallback_status': self.fallback_system.get_fallback_status(),
            'health_checks': self.health_checker.run_health_checks()
        }

    def export_monitoring_report(self, filepath: str = None):
        """Export comprehensive monitoring report."""
        if not filepath:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"monitoring_report_{timestamp}.json"

        self.monitor.export_metrics(filepath)
        return filepath
    
    def process_and_export(self, results: List[ScrapingResult], 
                          output_format: str = "csv", 
                          output_filename: Optional[str] = None) -> str:
        """Process results and export to specified format."""
        if not results:
            self.logger.warning("No results to process")
            return ""
        
        # Process results
        processed_results = self.data_processor.process_results(results)
        
        # Export results
        if output_format.lower() == "csv":
            return self.csv_exporter.export(processed_results, output_filename)
        elif output_format.lower() in ["xlsx", "excel"]:
            return self.excel_exporter.export(processed_results, output_filename)
        elif output_format.lower() == "finder":
            return self.excel_exporter.export_finder_compatible(processed_results, output_filename)
        else:
            self.logger.error(f"Unsupported output format: {output_format}")
            return ""
    
    def cleanup(self):
        """Clean up resources including monitoring systems."""
        # Stop monitoring
        if hasattr(self, 'monitor') and self.monitor.enabled:
            self.monitor.stop_monitoring()
            self.logger.info("Monitoring system stopped")

        # Clean up engine
        self.engine.cleanup()


@click.command()
@click.option('--business-type', '-b', multiple=True, 
              help='Business type to search for (e.g., "lawn care", "restaurant")')
@click.option('--location', '-l', multiple=True,
              help='Location to search in (e.g., "dallas tx", "houston")')
@click.option('--sources', '-s', multiple=True,
              help='Sources to scrape (bbb, manta, linkedin, truepeoplesearch, cyberbackgroundchecks)')
@click.option('--config', '-c', default='config.yaml',
              help='Configuration file path')
@click.option('--output', '-o', 
              help='Output filename')
@click.option('--format', '-f', default='csv', 
              type=click.Choice(['csv', 'xlsx', 'excel', 'finder']),
              help='Output format')
@click.option('--interactive', '-i', is_flag=True,
              help='Run in interactive mode')
@click.option('--verbose', '-v', is_flag=True,
              help='Enable verbose logging')
def main(business_type, location, sources, config, output, format, interactive, verbose):
    """Business Owner Scraper - Extract owner information from multiple sources."""
    
    # Set up logging
    log_level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/scraper.log')
        ]
    )
    
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)
    
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize scraper
        scraper = BusinessOwnerScraper(config)
        
        if interactive:
            run_interactive_mode(scraper)
        else:
            # Validate required parameters
            if not business_type:
                click.echo("Error: --business-type is required (or use --interactive)")
                sys.exit(1)
            
            if not location:
                click.echo("Error: --location is required (or use --interactive)")
                sys.exit(1)
            
            # Run scraping
            results = scraper.scrape(
                business_types=list(business_type),
                locations=list(location),
                sources=list(sources) if sources else None
            )
            
            if results:
                # Process and export
                output_file = scraper.process_and_export(results, format, output)
                
                if output_file:
                    click.echo(f"\n✅ Scraping completed successfully!")
                    click.echo(f"📊 Total results: {len(results)}")
                    click.echo(f"📁 Output file: {output_file}")
                else:
                    click.echo("❌ Error exporting results")
                    sys.exit(1)
            else:
                click.echo("⚠️  No results found")
        
    except KeyboardInterrupt:
        click.echo("\n🛑 Scraping interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        click.echo(f"❌ Error: {e}")
        sys.exit(1)
    finally:
        try:
            scraper.cleanup()
        except:
            pass


def run_interactive_mode(scraper: BusinessOwnerScraper):
    """Run scraper in interactive mode."""
    click.echo("🤖 Business Owner Scraper - Interactive Mode")
    click.echo("=" * 50)
    
    # Get business types
    business_types = []
    click.echo("\n📋 Enter business types to search for:")
    while True:
        business_type = click.prompt("Business type (or 'done' to finish)", default="").strip()
        if business_type.lower() == 'done' or not business_type:
            break
        business_types.append(business_type)
    
    if not business_types:
        click.echo("❌ No business types specified")
        return
    
    # Get locations
    locations = []
    click.echo("\n📍 Enter locations to search in:")
    while True:
        location = click.prompt("Location (or 'done' to finish)", default="").strip()
        if location.lower() == 'done' or not location:
            break
        locations.append(location)
    
    if not locations:
        click.echo("❌ No locations specified")
        return
    
    # Get sources
    available_sources = ['bbb', 'manta', 'linkedin', 'truepeoplesearch', 'cyberbackgroundchecks']
    click.echo(f"\n🔍 Available sources: {', '.join(available_sources)}")
    
    use_all = click.confirm("Use all available sources?", default=True)
    
    if use_all:
        sources = None  # Use all enabled sources
    else:
        sources = []
        for source in available_sources:
            if click.confirm(f"Use {source}?", default=True):
                sources.append(source)
    
    # Get output preferences
    output_format = click.prompt(
        "Output format", 
        type=click.Choice(['csv', 'xlsx', 'finder']),
        default='xlsx'
    )
    
    output_filename = click.prompt("Output filename (optional)", default="").strip()
    if not output_filename:
        output_filename = None
    
    # Confirm and run
    click.echo("\n📋 Scraping Configuration:")
    click.echo(f"   Business Types: {', '.join(business_types)}")
    click.echo(f"   Locations: {', '.join(locations)}")
    click.echo(f"   Sources: {'All enabled' if sources is None else ', '.join(sources)}")
    click.echo(f"   Output Format: {output_format}")
    click.echo(f"   Output File: {output_filename or 'Auto-generated'}")
    
    if not click.confirm("\n🚀 Start scraping?", default=True):
        click.echo("❌ Scraping cancelled")
        return
    
    # Run scraping
    click.echo("\n🔄 Starting scraping process...")
    
    results = scraper.scrape(business_types, locations, sources)
    
    if results:
        click.echo(f"\n✅ Found {len(results)} raw results")
        click.echo("🔄 Processing and exporting...")
        
        output_file = scraper.process_and_export(results, output_format, output_filename)
        
        if output_file:
            click.echo(f"\n🎉 Scraping completed successfully!")
            click.echo(f"📁 Results saved to: {output_file}")
        else:
            click.echo("❌ Error exporting results")
    else:
        click.echo("⚠️  No results found")


if __name__ == '__main__':
    main()
