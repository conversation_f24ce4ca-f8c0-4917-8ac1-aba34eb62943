#!/usr/bin/env python3
"""
Business Finder System - Streamlit Web Application
A comprehensive frontend for the Business Finder data scraping and enrichment system.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import os
import sys
import subprocess
import threading
import time
import queue
import io
from pathlib import Path

# Add the src directory to the Python path
sys.path.append(str(Path(__file__).parent / 'src'))

# Import the main scraping system
try:
    from main import BusinessFinderSystem
    from src.utils.data_processor import DataProcessor
    from src.models.scraping_result import ScrapingResult
except ImportError as e:
    st.error(f"Failed to import Business Finder System: {e}")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="Business Finder System",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    .status-working { background-color: #28a745; }
    .status-limited { background-color: #ffc107; }
    .status-blocked { background-color: #dc3545; }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .progress-container {
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'search_results' not in st.session_state:
    st.session_state.search_results = None
if 'search_history' not in st.session_state:
    st.session_state.search_history = []
if 'system_stats' not in st.session_state:
    st.session_state.system_stats = {
        'total_searches': 0,
        'total_results': 0,
        'total_cost': 0.0,
        'avg_processing_time': 0.0
    }

# Data source configuration with status indicators
DATA_SOURCES = {
    'manta_browser': {
        'name': 'Manta Browser (ScraperAPI)',
        'status': 'working',
        'description': '230 business listings per search, 100% success rate',
        'cost_per_search': 0.003,
        'avg_response_time': '6-8 seconds'
    },
    'truthfinder_browser': {
        'name': 'TruthFinder Browser',
        'status': 'working',
        'description': '131 personal records with demographics',
        'cost_per_search': 0.0,
        'avg_response_time': '44 seconds'
    },
    'bbb': {
        'name': 'Better Business Bureau',
        'status': 'limited',
        'description': 'Limited functionality, requires Google API',
        'cost_per_search': 0.0,
        'avg_response_time': 'N/A'
    },
    'manta': {
        'name': 'Traditional Manta',
        'status': 'limited',
        'description': 'Superseded by Manta Browser',
        'cost_per_search': 0.0,
        'avg_response_time': 'N/A'
    },
    'cyberbackgroundchecks': {
        'name': 'CyberBackgroundChecks',
        'status': 'blocked',
        'description': 'Blocked by Cloudflare protection',
        'cost_per_search': 0.0,
        'avg_response_time': 'Timeout'
    }
}

def get_status_indicator(status):
    """Return HTML for status indicator"""
    status_class = f"status-{status}"
    return f'<span class="{status_class} status-indicator"></span>'

def format_currency(amount):
    """Format currency values"""
    return f"${amount:.4f}" if amount < 0.01 else f"${amount:.2f}"

def main():
    """Main application function"""
    
    # Header
    st.markdown('<h1 class="main-header">🔍 Business Finder System</h1>', unsafe_allow_html=True)
    st.markdown("**Comprehensive Business Intelligence & Data Enrichment Platform**")
    
    # Sidebar for navigation
    st.sidebar.title("Navigation")
    tab_selection = st.sidebar.radio(
        "Select Section:",
        ["🔍 Search Configuration", "📊 Results & Analytics", "⚙️ System Status", "📈 Usage Dashboard"]
    )
    
    # Main content based on tab selection
    if tab_selection == "🔍 Search Configuration":
        search_configuration_tab()
    elif tab_selection == "📊 Results & Analytics":
        results_analytics_tab()
    elif tab_selection == "⚙️ System Status":
        system_status_tab()
    elif tab_selection == "📈 Usage Dashboard":
        usage_dashboard_tab()

def search_configuration_tab():
    """Search configuration interface"""
    st.header("🔍 Search Configuration")
    
    # Create two columns for the form
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Search Parameters")
        
        # Business type input
        business_type = st.text_input(
            "Business Type",
            value="restaurant",
            help="Enter the type of business to search for (e.g., restaurant, construction, law firm)"
        )
        
        # Location input
        location = st.text_input(
            "Location",
            value="houston tx",
            help="Enter the location to search in (e.g., houston tx, miami fl, dallas tx)"
        )
        
        # Data sources selection
        st.subheader("Data Sources")
        st.markdown("Select which data sources to use for your search:")
        
        selected_sources = []
        for source_key, source_info in DATA_SOURCES.items():
            col_check, col_info = st.columns([1, 4])
            
            with col_check:
                if st.checkbox(
                    source_info['name'],
                    value=source_info['status'] == 'working',
                    disabled=source_info['status'] == 'blocked',
                    key=f"source_{source_key}"
                ):
                    selected_sources.append(source_key)
            
            with col_info:
                status_html = get_status_indicator(source_info['status'])
                st.markdown(
                    f"{status_html} {source_info['description']} "
                    f"(Cost: {format_currency(source_info['cost_per_search'])}, "
                    f"Response: {source_info['avg_response_time']})",
                    unsafe_allow_html=True
                )
        
        # Export format selection
        export_format = st.selectbox(
            "Export Format",
            ["excel", "csv"],
            help="Choose the format for exporting results"
        )
        
        # Advanced options
        with st.expander("Advanced Options"):
            max_pages = st.slider(
                "Maximum Pages per Source",
                min_value=1,
                max_value=5,
                value=3,
                help="Limit the number of pages to scrape per source"
            )
            
            verbose_logging = st.checkbox(
                "Enable Verbose Logging",
                value=True,
                help="Show detailed logging information during scraping"
            )
    
    with col2:
        st.subheader("Search Preview")
        
        # Estimated results and cost
        estimated_results = 0
        estimated_cost = 0.0
        estimated_time = 0
        
        for source in selected_sources:
            if source == 'manta_browser':
                estimated_results += 230
                estimated_cost += DATA_SOURCES[source]['cost_per_search']
                estimated_time += 25  # 3 pages * 8 seconds
            elif source == 'truthfinder_browser':
                estimated_results += 131
                estimated_time += 44
        
        # Display estimates
        st.metric("Estimated Raw Results", f"{estimated_results:,}")
        st.metric("Estimated Cost", format_currency(estimated_cost))
        st.metric("Estimated Time", f"{estimated_time} seconds")
        
        # Validation and enrichment info
        st.info(
            f"**Expected Final Results:** ~{int(estimated_results * 0.42)} enriched profiles\n\n"
            f"**Validation Rate:** ~68% (based on historical data)\n\n"
            f"**Data Enrichment:** Cross-source merging enabled"
        )
    
    # Search button and execution
    st.markdown("---")
    
    col_search, col_clear = st.columns([3, 1])
    
    with col_search:
        if st.button("🚀 Start Search", type="primary", use_container_width=True):
            if not business_type or not location:
                st.error("Please enter both business type and location.")
            elif not selected_sources:
                st.error("Please select at least one data source.")
            else:
                execute_search(business_type, location, selected_sources, export_format, verbose_logging)
    
    with col_clear:
        if st.button("🗑️ Clear Results", use_container_width=True):
            st.session_state.search_results = None
            st.rerun()

def execute_search(business_type, location, sources, export_format, verbose):
    """Execute the search with progress tracking"""
    
    # Create progress indicators
    progress_bar = st.progress(0)
    status_text = st.empty()
    log_container = st.empty()
    
    # Initialize search
    status_text.text("Initializing search...")
    progress_bar.progress(10)
    
    try:
        # Create command
        cmd = [
            "python3", "main.py",
            "--business-type", business_type,
            "--location", location,
            "--format", export_format
        ]
        
        # Add sources
        for source in sources:
            cmd.extend(["--sources", source])
        
        if verbose:
            cmd.append("--verbose")
        
        # Execute search
        status_text.text("Executing search...")
        progress_bar.progress(30)
        
        # Run the command and capture output
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Monitor progress
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                output_lines.append(output.strip())
                
                # Update progress based on log content
                if "Scraping" in output:
                    progress_bar.progress(50)
                    status_text.text("Scraping data sources...")
                elif "Processing" in output:
                    progress_bar.progress(70)
                    status_text.text("Processing and enriching data...")
                elif "Exported" in output:
                    progress_bar.progress(90)
                    status_text.text("Exporting results...")
                
                # Show recent log lines
                if verbose:
                    recent_logs = "\n".join(output_lines[-10:])
                    log_container.text_area("Recent Logs", recent_logs, height=200)
        
        # Check if successful
        return_code = process.poll()
        if return_code == 0:
            progress_bar.progress(100)
            status_text.text("✅ Search completed successfully!")
            
            # Parse results from output
            parse_search_results(output_lines, business_type, location, sources)
            
            st.success("Search completed! Check the Results & Analytics tab to view your data.")
            
        else:
            st.error(f"Search failed with return code: {return_code}")
            st.text_area("Error Output", "\n".join(output_lines), height=300)
            
    except Exception as e:
        st.error(f"Error executing search: {str(e)}")
        progress_bar.progress(0)
        status_text.text("❌ Search failed")

def parse_search_results(output_lines, business_type, location, sources):
    """Parse search results from command output"""
    
    # Extract key metrics from output
    total_results = 0
    processing_time = 0
    
    for line in output_lines:
        if "Total results:" in line:
            try:
                total_results = int(line.split("Total results:")[1].strip())
            except:
                pass
        elif "Output file:" in line:
            output_file = line.split("Output file:")[1].strip()
            
            # Try to load the results file
            try:
                if output_file.endswith('.xlsx'):
                    df = pd.read_excel(output_file)
                else:
                    df = pd.read_csv(output_file)
                
                # Store results in session state
                st.session_state.search_results = {
                    'data': df,
                    'metadata': {
                        'business_type': business_type,
                        'location': location,
                        'sources': sources,
                        'total_results': len(df),
                        'timestamp': datetime.now(),
                        'output_file': output_file
                    }
                }
                
                # Update search history
                st.session_state.search_history.append({
                    'timestamp': datetime.now(),
                    'business_type': business_type,
                    'location': location,
                    'sources': sources,
                    'results_count': len(df)
                })
                
                # Update system stats
                st.session_state.system_stats['total_searches'] += 1
                st.session_state.system_stats['total_results'] += len(df)
                
            except Exception as e:
                st.error(f"Failed to load results file: {e}")

def results_analytics_tab():
    """Results and analytics display"""
    st.header("📊 Results & Analytics")
    
    if st.session_state.search_results is None:
        st.info("No search results available. Please run a search first.")
        return
    
    results_data = st.session_state.search_results
    df = results_data['data']
    metadata = results_data['metadata']
    
    # Results summary
    st.subheader("Search Summary")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Results", f"{len(df):,}")
    with col2:
        st.metric("Business Type", metadata['business_type'].title())
    with col3:
        st.metric("Location", metadata['location'].title())
    with col4:
        st.metric("Sources Used", len(metadata['sources']))
    
    # Data quality metrics
    st.subheader("Data Quality Metrics")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Calculate completion rates for key fields
        business_name_rate = (df['business_name'].notna().sum() / len(df)) * 100
        st.metric("Business Names", f"{business_name_rate:.1f}%")
    
    with col2:
        address_rate = (df['address'].notna().sum() / len(df)) * 100
        st.metric("Addresses", f"{address_rate:.1f}%")
    
    with col3:
        phone_rate = (df['phone'].notna().sum() / len(df)) * 100
        st.metric("Phone Numbers", f"{phone_rate:.1f}%")
    
    # Source distribution
    if 'source' in df.columns:
        st.subheader("Source Distribution")
        source_counts = df['source'].value_counts()
        
        fig = px.pie(
            values=source_counts.values,
            names=source_counts.index,
            title="Results by Data Source"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # Interactive data table
    st.subheader("Results Data")
    
    # Filtering options
    col1, col2 = st.columns(2)
    
    with col1:
        if 'business_name' in df.columns:
            search_term = st.text_input("Search Business Names", "")
            if search_term:
                df = df[df['business_name'].str.contains(search_term, case=False, na=False)]
    
    with col2:
        if 'city' in df.columns:
            cities = df['city'].dropna().unique()
            selected_city = st.selectbox("Filter by City", ["All"] + list(cities))
            if selected_city != "All":
                df = df[df['city'] == selected_city]
    
    # Display data table
    st.dataframe(
        df,
        use_container_width=True,
        height=400
    )
    
    # Export options
    st.subheader("Export Options")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📥 Download CSV"):
            csv = df.to_csv(index=False)
            st.download_button(
                label="Download CSV File",
                data=csv,
                file_name=f"business_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
    
    with col2:
        if st.button("📥 Download Excel"):
            buffer = io.BytesIO()
            with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='Results', index=False)
            
            st.download_button(
                label="Download Excel File",
                data=buffer.getvalue(),
                file_name=f"business_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
    
    with col3:
        if st.button("📥 Download JSON"):
            json_data = df.to_json(orient='records', indent=2)
            st.download_button(
                label="Download JSON File",
                data=json_data,
                file_name=f"business_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

def system_status_tab():
    """System status and configuration"""
    st.header("⚙️ System Status")

    # Data source status overview
    st.subheader("Data Source Status")

    for source_key, source_info in DATA_SOURCES.items():
        with st.expander(f"{source_info['name']} - {source_info['status'].title()}", expanded=False):
            col1, col2 = st.columns([1, 2])

            with col1:
                status_color = {
                    'working': '🟢',
                    'limited': '🟡',
                    'blocked': '🔴'
                }
                st.markdown(f"**Status:** {status_color[source_info['status']]} {source_info['status'].title()}")
                st.markdown(f"**Cost per Search:** {format_currency(source_info['cost_per_search'])}")
                st.markdown(f"**Response Time:** {source_info['avg_response_time']}")

            with col2:
                st.markdown(f"**Description:** {source_info['description']}")

                # Add specific recommendations
                if source_info['status'] == 'working':
                    st.success("✅ Recommended for production use")
                elif source_info['status'] == 'limited':
                    st.warning("⚠️ Limited functionality - consider improvements")
                else:
                    st.error("❌ Currently blocked - disable or find alternatives")

    # System configuration
    st.subheader("System Configuration")

    # Check if config file exists
    config_path = Path("config.yaml")
    if config_path.exists():
        st.success("✅ Configuration file found")

        # Show key configuration settings
        try:
            import yaml
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Display ScraperAPI configuration
            if 'scrapers' in config and 'manta_browser' in config['scrapers']:
                manta_config = config['scrapers']['manta_browser']
                if 'scraper_api' in manta_config:
                    api_config = manta_config['scraper_api']
                    st.info(f"ScraperAPI Status: {'Enabled' if api_config.get('enabled') else 'Disabled'}")
                    if api_config.get('api_key'):
                        masked_key = api_config['api_key'][:8] + "..." + api_config['api_key'][-4:]
                        st.info(f"API Key: {masked_key}")

        except Exception as e:
            st.warning(f"Could not parse configuration: {e}")
    else:
        st.error("❌ Configuration file not found")

    # System requirements check
    st.subheader("System Requirements")

    # Check Python version
    python_version = sys.version.split()[0]
    if python_version >= "3.9":
        st.success(f"✅ Python {python_version} (Compatible)")
    else:
        st.error(f"❌ Python {python_version} (Requires 3.9+)")

    # Check required packages
    required_packages = [
        'streamlit', 'pandas', 'plotly', 'requests',
        'beautifulsoup4', 'selenium', 'playwright'
    ]

    for package in required_packages:
        try:
            __import__(package)
            st.success(f"✅ {package}")
        except ImportError:
            st.error(f"❌ {package} (Not installed)")

    # Performance metrics
    st.subheader("Performance Metrics")

    # Mock performance data (in production, this would come from actual monitoring)
    perf_data = {
        'Metric': ['Average Response Time', 'Success Rate', 'Data Quality Score', 'Cost Efficiency'],
        'Value': ['2.3 minutes', '100%', '68%', '$0.00003/record'],
        'Status': ['Good', 'Excellent', 'Good', 'Excellent']
    }

    perf_df = pd.DataFrame(perf_data)
    st.dataframe(perf_df, use_container_width=True)

def usage_dashboard_tab():
    """Usage analytics and dashboard"""
    st.header("📈 Usage Dashboard")

    # Overall statistics
    st.subheader("Overall Statistics")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Total Searches",
            st.session_state.system_stats['total_searches'],
            delta=None
        )

    with col2:
        st.metric(
            "Total Results",
            f"{st.session_state.system_stats['total_results']:,}",
            delta=None
        )

    with col3:
        st.metric(
            "Total Cost",
            format_currency(st.session_state.system_stats['total_cost']),
            delta=None
        )

    with col4:
        avg_results = (st.session_state.system_stats['total_results'] /
                      max(st.session_state.system_stats['total_searches'], 1))
        st.metric(
            "Avg Results/Search",
            f"{avg_results:.0f}",
            delta=None
        )

    # Search history
    if st.session_state.search_history:
        st.subheader("Search History")

        # Convert search history to DataFrame
        history_df = pd.DataFrame(st.session_state.search_history)
        history_df['timestamp'] = pd.to_datetime(history_df['timestamp'])

        # Display recent searches
        st.dataframe(
            history_df.sort_values('timestamp', ascending=False).head(10),
            use_container_width=True
        )

        # Usage trends
        st.subheader("Usage Trends")

        # Searches over time
        if len(history_df) > 1:
            daily_searches = history_df.groupby(history_df['timestamp'].dt.date).size()

            fig = px.line(
                x=daily_searches.index,
                y=daily_searches.values,
                title="Daily Search Volume",
                labels={'x': 'Date', 'y': 'Number of Searches'}
            )
            st.plotly_chart(fig, use_container_width=True)

        # Popular business types
        business_type_counts = history_df['business_type'].value_counts()
        if len(business_type_counts) > 0:
            fig = px.bar(
                x=business_type_counts.index,
                y=business_type_counts.values,
                title="Most Searched Business Types",
                labels={'x': 'Business Type', 'y': 'Search Count'}
            )
            st.plotly_chart(fig, use_container_width=True)

        # Popular locations
        location_counts = history_df['location'].value_counts()
        if len(location_counts) > 0:
            fig = px.bar(
                x=location_counts.index,
                y=location_counts.values,
                title="Most Searched Locations",
                labels={'x': 'Location', 'y': 'Search Count'}
            )
            st.plotly_chart(fig, use_container_width=True)

    else:
        st.info("No search history available yet. Run some searches to see analytics.")

    # Cost analysis
    st.subheader("Cost Analysis")

    # Estimated monthly costs based on usage
    if st.session_state.search_history:
        recent_searches = len([h for h in st.session_state.search_history
                             if h['timestamp'] > datetime.now() - timedelta(days=30)])

        estimated_monthly_cost = recent_searches * 0.003  # Average cost per search

        col1, col2 = st.columns(2)

        with col1:
            st.metric("Searches This Month", recent_searches)
            st.metric("Estimated Monthly Cost", format_currency(estimated_monthly_cost))

        with col2:
            # Cost breakdown by source
            cost_breakdown = {
                'Manta Browser (ScraperAPI)': estimated_monthly_cost,
                'TruthFinder Browser': 0.0,
                'Other Sources': 0.0
            }

            fig = px.pie(
                values=list(cost_breakdown.values()),
                names=list(cost_breakdown.keys()),
                title="Cost Breakdown by Source"
            )
            st.plotly_chart(fig, use_container_width=True)

    # Export usage data
    st.subheader("Export Usage Data")

    if st.session_state.search_history:
        if st.button("📥 Download Usage Report"):
            usage_df = pd.DataFrame(st.session_state.search_history)
            csv = usage_df.to_csv(index=False)
            st.download_button(
                label="Download Usage CSV",
                data=csv,
                file_name=f"usage_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

if __name__ == "__main__":
    main()
