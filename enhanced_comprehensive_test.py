#!/usr/bin/env python3
"""
Enhanced Comprehensive Test - Demonstrates the new comprehensive data extraction capabilities.
Tests all scrapers with enhanced information extraction.
"""

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_enhanced_extraction():
    """Test the enhanced comprehensive data extraction."""
    print("🚀 Enhanced Business Owner Scraper - Comprehensive Data Test")
    print("=" * 80)
    print("🎯 Testing enhanced extraction with comprehensive business information")
    print("=" * 80)
    
    try:
        # Import modules
        from src.core import ScrapingEngine, ScrapingResult
        from src.scrapers.bbb_scraper import BBBScraper
        from src.scrapers.manta_scraper import MantaScraper
        from src.scrapers.truthfinder_scraper import TruthFinderScraper
        from src.utils.data_processor import DataProcessor
        from src.exporters.excel_exporter import ExcelExporter
        
        print("✅ All enhanced modules imported successfully")
        
        # Initialize engine
        print("\n🔧 Initializing enhanced scraping engine...")
        engine = ScrapingEngine("config.yaml")
        processor = DataProcessor(engine.config)
        excel_exporter = ExcelExporter(engine.config)
        
        # Test scenarios with enhanced data extraction
        test_scenarios = [
            {
                'business_type': 'restaurant',
                'location': 'houston tx',
                'description': 'Restaurant owners with comprehensive business data'
            },
            {
                'business_type': 'construction',
                'location': 'dallas tx', 
                'description': 'Construction company owners with detailed information'
            }
        ]
        
        all_enhanced_results = []
        
        # Test each scraper with enhanced extraction
        scrapers_to_test = {
            'BBB Enhanced': BBBScraper(engine),
            'Manta Enhanced': MantaScraper(engine),
            'TruthFinder API Enhanced': TruthFinderScraper(engine)
        }
        
        for scenario_idx, scenario in enumerate(test_scenarios, 1):
            business_type = scenario['business_type']
            location = scenario['location']
            description = scenario['description']
            
            print(f"\n🧪 ENHANCED TEST {scenario_idx}/2: {description}")
            print(f"   Business Type: {business_type}")
            print(f"   Location: {location}")
            print("-" * 60)
            
            scenario_results = []
            
            for scraper_name, scraper in scrapers_to_test.items():
                if not scraper.is_enabled():
                    print(f"   ⚠️  {scraper_name} is disabled, skipping...")
                    continue
                
                print(f"\n📊 Testing {scraper_name}...")
                
                try:
                    # Test the enhanced scraper
                    results = scraper.search(business_type, location)
                    
                    if results:
                        print(f"   ✅ {scraper_name}: {len(results)} enhanced results")
                        
                        # Display sample of enhanced data
                        sample_result = results[0]
                        print(f"   📋 Sample Enhanced Data:")
                        print(f"      Owner: {sample_result.owner_name}")
                        print(f"      Business: {sample_result.business_name}")
                        print(f"      Phone: {sample_result.phone}")
                        print(f"      Email: {sample_result.email}")
                        print(f"      Address: {sample_result.address}")
                        print(f"      Street: {sample_result.street_address}")
                        print(f"      City: {sample_result.city}")
                        print(f"      State: {sample_result.state}")
                        print(f"      Zip: {sample_result.zip_code}")
                        print(f"      Website: {sample_result.website}")
                        print(f"      Description: {sample_result.business_description}")
                        print(f"      Years in Business: {sample_result.years_in_business}")
                        print(f"      Employee Count: {sample_result.employee_count}")
                        print(f"      BBB Rating: {sample_result.bbb_rating}")
                        print(f"      Google Rating: {sample_result.google_rating}")
                        print(f"      LinkedIn: {sample_result.linkedin_url}")
                        print(f"      Facebook: {sample_result.facebook_url}")
                        print(f"      Business Structure: {sample_result.business_structure}")
                        print(f"      Executives: {len(sample_result.executives)} found")
                        print(f"      Previous Addresses: {len(sample_result.previous_addresses)} found")
                        print(f"      Confidence Score: {sample_result.confidence_score}")
                        print(f"      Data Quality: {sample_result.data_quality}")
                        print(f"      Google Snippet: {sample_result.google_snippet[:100]}..." if sample_result.google_snippet else "")
                        
                        scenario_results.extend(results)
                    else:
                        print(f"   ⚠️  {scraper_name}: No results found")
                        
                except Exception as e:
                    print(f"   ❌ {scraper_name} error: {e}")
                    continue
            
            all_enhanced_results.extend(scenario_results)
            print(f"\n📊 Scenario {scenario_idx} Total: {len(scenario_results)} enhanced results")
        
        print(f"\n🎯 ENHANCED EXTRACTION SUMMARY")
        print("=" * 50)
        print(f"📊 Total Enhanced Results: {len(all_enhanced_results)}")
        
        if all_enhanced_results:
            # Process results with enhanced data processor
            print("\n🔄 Processing enhanced results...")
            processed_results = processor.process_results(all_enhanced_results)
            
            print(f"✅ Processed {len(processed_results)} enhanced results")
            
            # Export enhanced results
            print("\n📁 Exporting enhanced results...")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Export comprehensive Excel file
            excel_file = excel_exporter.export(processed_results, f"enhanced_comprehensive_{timestamp}.xlsx")
            if excel_file:
                print(f"✅ Enhanced Excel export: {excel_file}")
            
            # Export finder-compatible format
            finder_file = excel_exporter.export_finder_compatible(processed_results, f"enhanced_finder_{timestamp}.xlsx")
            if finder_file:
                print(f"✅ Enhanced Finder export: {finder_file}")
            
            # Display enhanced statistics
            print(f"\n📈 ENHANCED DATA STATISTICS")
            print("=" * 40)
            
            stats = {
                'results_with_phone': len([r for r in processed_results if r.phone]),
                'results_with_email': len([r for r in processed_results if r.email]),
                'results_with_website': len([r for r in processed_results if r.website]),
                'results_with_detailed_address': len([r for r in processed_results if r.street_address and r.city]),
                'results_with_business_description': len([r for r in processed_results if r.business_description]),
                'results_with_years_in_business': len([r for r in processed_results if r.years_in_business]),
                'results_with_employee_count': len([r for r in processed_results if r.employee_count]),
                'results_with_ratings': len([r for r in processed_results if r.bbb_rating or r.google_rating]),
                'results_with_social_media': len([r for r in processed_results if r.linkedin_url or r.facebook_url]),
                'results_with_executives': len([r for r in processed_results if r.executives]),
                'results_with_previous_addresses': len([r for r in processed_results if r.previous_addresses]),
                'high_confidence_results': len([r for r in processed_results if r.confidence_score and r.confidence_score > 0.8])
            }
            
            for stat_name, count in stats.items():
                percentage = (count / len(processed_results)) * 100 if processed_results else 0
                print(f"   {stat_name.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
            
            print(f"\n🎉 Enhanced comprehensive test completed successfully!")
            print(f"📊 Data richness significantly improved with comprehensive extraction!")
            
        else:
            print("⚠️  No enhanced results to process")
        
    except Exception as e:
        print(f"❌ Enhanced test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_enhanced_extraction()
