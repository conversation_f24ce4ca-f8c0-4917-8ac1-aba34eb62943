#!/usr/bin/env python3
"""
Manta Browser Scraper - Uses Playwright to make API calls within browser context.
This bypasses Cloudflare protection for Manta's internal API endpoints.
"""

import asyncio
import logging
from typing import List, Dict, Any
from datetime import datetime
from playwright.async_api import async_playwright

from src.scrapers.base_scraper import BaseScraper
from src.core import ScrapingResult

logger = logging.getLogger(__name__)

class MantaBrowserScraper(BaseScraper):
    """Manta scraper using browser-based API calls to bypass anti-bot protection."""
    
    def __init__(self, engine):
        super().__init__(engine, "manta_browser")
        self.base_url = "https://www.manta.com"
        
        # Business type to category ID mapping (from your curl example)
        self.category_mappings = {
            'restaurant': '54_C4_000',
            'construction': '54_B1_000', 
            'lawn care': '54_B3_KCQ',
            'plumbing': '54_B1_KCQ',
            'electrical': '54_B1_KCQ',
            'automotive': '54_A1_000',
            'dental': '54_D0_KCQ',
            'medical': '54_D0_000',
            'legal': '54_A6_KCQ',
            'accounting': '54_A6_000',
            'real estate': '54_A6_KCQ',
            'insurance': '54_A6_000',
            'dolls_and_stuffed_toys': '54_B83AE_KCQ',  # From your example
            'retail': '54_C0_000',
            'manufacturing': '54_B0_000',
            'services': '54_A0_000'
        }
        
        logger.info("Manta Browser scraper initialized")
    
    def is_enabled(self) -> bool:
        """Check if Manta browser scraper is enabled."""
        return self.engine.config.get('sources', {}).get('manta_browser', {}).get('enabled', True)
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """
        Search for businesses using Manta's internal API via browser.
        
        Args:
            business_type: Type of business to search for
            location: Location to search in
            
        Returns:
            List of ScrapingResult objects
        """
        if not self.is_enabled():
            logger.info("Manta browser scraper is disabled")
            return []
        
        logger.info(f"Searching Manta browser API for {business_type} in {location}")
        
        try:
            # Get category ID for business type
            category_id = self._get_category_id(business_type)
            
            # Parse location for URL formatting
            location_slug = self._format_location_for_url(location)
            business_slug = self._format_business_type_for_url(business_type)
            
            # Search multiple pages
            max_pages = self.engine.config.get('sources', {}).get('manta_browser', {}).get('max_pages', 3)
            all_results = []
            
            for page in range(1, max_pages + 1):
                try:
                    logger.info(f"Searching Manta page {page} for {business_type} in {location}")
                    
                    # Use asyncio to run the browser search
                    api_data = asyncio.run(self._search_browser_api(category_id, business_slug, location_slug, page))
                    
                    if api_data:
                        # Convert API data to ScrapingResult objects
                        results = self._convert_api_data_to_results(api_data, business_type, location)
                        all_results.extend(results)
                        
                        logger.info(f"Found {len(results)} results on page {page}")
                        
                        # If no results on this page, stop searching
                        if not results:
                            break
                    else:
                        logger.info(f"No results found on page {page}")
                        break
                    
                except Exception as e:
                        logger.error(f"Error searching page {page}: {e}")
                        continue
            
            logger.info(f"Manta browser API returned {len(all_results)} total results")
            return all_results
            
        except Exception as e:
            logger.error(f"Manta browser search failed: {e}")
            return []
    
    async def _search_browser_api(self, category_id: str, business_slug: str, location_slug: str, page: int):
        """Search Manta API using browser context."""
        
        async with async_playwright() as p:
            # Launch browser (headless for production)
            browser = await p.chromium.launch(
                headless=True,  # Set to False for debugging
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage'
                ]
            )
            
            # Create realistic browser context
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            # Add stealth script
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            page_obj = await context.new_page()
            
            try:
                # Navigate to Manta to establish session
                await page_obj.goto("https://www.manta.com", wait_until="networkidle")
                await page_obj.wait_for_timeout(3000)
                
                # Handle any modals/popups
                try:
                    # Look for cookie consent or other popups
                    accept_button = await page_obj.query_selector('button:has-text("Accept"), button:has-text("OK"), .cookie-accept')
                    if accept_button:
                        await accept_button.click()
                        await page_obj.wait_for_timeout(2000)
                except:
                    pass
                
                # Build API URL using the pattern from your curl command
                api_url = f"{self.base_url}/more-results/{category_id}/{business_slug}"
                if location_slug:
                    # Some endpoints use location in URL, some use query params
                    api_url = f"{self.base_url}/mb_{category_id}/{business_slug}/{location_slug}"
                
                params = {'pg': page}
                
                # Convert params to URL string
                param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
                full_url = f"{api_url}?{param_string}"
                
                logger.debug(f"Manta API URL: {full_url}")
                
                # Make the API call using browser's fetch API
                api_response = await page_obj.evaluate(f"""
                    async () => {{
                        try {{
                            const response = await fetch('{full_url}', {{
                                method: 'GET',
                                headers: {{
                                    'Accept': '*/*',
                                    'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7',
                                    'Cache-Control': 'no-cache',
                                    'Connection': 'keep-alive',
                                    'Pragma': 'no-cache',
                                    'Referer': 'https://www.manta.com/',
                                    'Sec-Fetch-Dest': 'empty',
                                    'Sec-Fetch-Mode': 'cors',
                                    'Sec-Fetch-Site': 'same-origin'
                                }}
                            }});
                            
                            if (response.ok) {{
                                const text = await response.text();
                                return {{ success: true, data: text, status: response.status }};
                            }} else {{
                                const text = await response.text();
                                return {{ success: false, error: text, status: response.status }};
                            }}
                        }} catch (error) {{
                            return {{ success: false, error: error.message, status: 0 }};
                        }}
                    }}
                """)
                
                await browser.close()
                
                if api_response['success']:
                    return api_response['data']
                else:
                    logger.warning(f"Manta API call failed: Status {api_response['status']}")
                    return None
                    
            except Exception as e:
                logger.error(f"Manta browser API call failed: {e}")
                await browser.close()
                return None
    
    def _convert_api_data_to_results(self, html_data: str, business_type: str, location: str) -> List[ScrapingResult]:
        """Convert Manta API HTML response to ScrapingResult objects."""
        results = []
        
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_data, 'html.parser')
            
            # Look for business listings in the HTML response
            business_selectors = [
                '.business-card',
                '.listing-item', 
                '.company-listing',
                '[data-business-id]',
                '.search-result',
                '.result-item',
                '.business-result'
            ]
            
            businesses = []
            for selector in business_selectors:
                businesses = soup.select(selector)
                if businesses:
                    logger.debug(f"Found {len(businesses)} businesses with selector: {selector}")
                    break
            
            if not businesses:
                # Try more generic selectors
                businesses = soup.select('div[class*="business"], div[class*="company"], div[class*="listing"]')
                logger.debug(f"Found {len(businesses)} businesses with generic selectors")
            
            for business in businesses:
                try:
                    result = self._extract_business_data(business, business_type, location)
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.debug(f"Error extracting business data: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error parsing Manta API response: {e}")
        
        return results
    
    def _extract_business_data(self, business_element, business_type: str, location: str) -> ScrapingResult:
        """Extract comprehensive business data from a business listing element."""
        try:
            # Extract business name
            business_name = self._extract_text(business_element, [
                '.business-name', '.company-name', '.listing-title', 'h2', 'h3', 'h4',
                '[class*="name"]', '.title'
            ])
            
            # Extract owner/contact name
            owner_name = self._extract_text(business_element, [
                '.owner-name', '.contact-name', '.manager-name', '.principal-name',
                '[class*="owner"]', '[class*="contact"]'
            ])
            
            # Extract phone
            phone = self._extract_text(business_element, [
                '.phone', '.tel', '[href^="tel:"]', '.contact-phone',
                '[class*="phone"]'
            ])
            
            # Extract email
            email = self._extract_text(business_element, [
                '.email', '[href^="mailto:"]', '.contact-email',
                '[class*="email"]'
            ])
            
            # Extract website
            website = self._extract_link(business_element, [
                '.website', '.url', '.company-website', 'a[href*="http"]'
            ])
            
            # Extract address
            address = self._extract_text(business_element, [
                '.address', '.location', '.business-address', '.street-address',
                '[class*="address"]', '[class*="location"]'
            ])
            
            # Parse address components
            street_address, city, state, zip_code = self._parse_address(address)
            
            # Extract business URL
            business_url = self._extract_link(business_element, [
                'a[href*="/c/"]', '.business-link', '.company-link', '.profile-link'
            ])
            
            if business_url and not business_url.startswith('http'):
                business_url = f"https://www.manta.com{business_url}"
            
            # Extract business description
            business_description = self._extract_text(business_element, [
                '.description', '.business-description', '.company-description',
                '.summary', '[class*="description"]'
            ])
            
            # Extract years in business
            years_in_business = self._extract_years_in_business(business_element)
            
            # Extract employee count
            employee_count = self._extract_employee_count(business_element)
            
            # Create ScrapingResult
            result = ScrapingResult(
                owner_name=owner_name,
                business_name=business_name,
                business_type=business_type,
                location=location,
                source=self.source_name,
                url=business_url,
                phone=phone,
                email=email,
                website=website,
                address=address,
                street_address=street_address,
                city=city,
                state=state,
                zip_code=zip_code,
                business_description=business_description,
                years_in_business=years_in_business,
                employee_count=employee_count,
                scraped_at=datetime.now(),
                confidence_score=0.8,  # High confidence for API data
                data_quality='high',
                verification_status='api_verified'
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting business data: {e}")
            return None
    
    def _get_category_id(self, business_type: str) -> str:
        """Get Manta category ID for business type."""
        business_lower = business_type.lower()
        
        # Try exact match first
        if business_lower in self.category_mappings:
            return self.category_mappings[business_lower]
        
        # Try partial matches
        for key, category_id in self.category_mappings.items():
            if key in business_lower or business_lower in key:
                return category_id
        
        # Default to general services
        return '54_A0_000'
    
    def _format_location_for_url(self, location: str) -> str:
        """Format location for URL slug."""
        return location.lower().replace(' ', '_').replace(',', '').replace('.', '')
    
    def _format_business_type_for_url(self, business_type: str) -> str:
        """Format business type for URL slug."""
        return business_type.lower().replace(' ', '_').replace('&', 'and')
    
    def _extract_text(self, element, selectors: List[str]) -> str:
        """Extract text using multiple selector options."""
        for selector in selectors:
            found = element.select_one(selector)
            if found:
                text = found.get_text(strip=True)
                if text:
                    return text
        return ""
    
    def _extract_link(self, element, selectors: List[str]) -> str:
        """Extract link URL using multiple selector options."""
        for selector in selectors:
            found = element.select_one(selector)
            if found:
                href = found.get('href')
                if href:
                    return href
        return ""
    
    def _parse_address(self, address: str) -> tuple:
        """Parse address into components."""
        if not address:
            return "", "", "", ""
        
        try:
            # Simple address parsing
            parts = address.split(',')
            if len(parts) >= 3:
                street = parts[0].strip()
                city = parts[1].strip()
                state_zip = parts[2].strip().split()
                state = state_zip[0] if state_zip else ""
                zip_code = state_zip[1] if len(state_zip) > 1 else ""
                return street, city, state, zip_code
            else:
                return address, "", "", ""
        except:
            return address, "", "", ""
    
    def _extract_years_in_business(self, element) -> str:
        """Extract years in business from element."""
        import re
        text = element.get_text()
        
        patterns = [
            r'(\d+)\s*years?\s*in\s*business',
            r'established\s*(?:in\s*)?(\d{4})',
            r'since\s*(\d{4})',
            r'founded\s*(?:in\s*)?(\d{4})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        return ""
    
    def _extract_employee_count(self, element) -> str:
        """Extract employee count from element."""
        import re
        text = element.get_text()
        
        patterns = [
            r'(\d+)\s*employees?',
            r'staff\s*of\s*(\d+)',
            r'(\d+)\s*(?:full-time|part-time)?\s*(?:staff|workers?)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        return ""
    
    def _extract_listing_urls(self, soup, business_type: str, location: str) -> List[str]:
        """Required by BaseScraper but not used in browser API mode."""
        return []
    
    def extract_owner_info(self, soup, url: str) -> List[ScrapingResult]:
        """Required by BaseScraper but not used in browser API mode."""
        return []
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get scraper health status."""
        try:
            return {
                'scraper': 'manta_browser',
                'status': 'healthy',
                'enabled': self.is_enabled(),
                'method': 'browser_api',
                'playwright_available': True
            }
        except Exception as e:
            return {
                'scraper': 'manta_browser',
                'status': 'unhealthy',
                'error': str(e),
                'enabled': self.is_enabled()
            }
