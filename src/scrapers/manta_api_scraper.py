#!/usr/bin/env python3
"""
Manta API Scraper
Uses Manta's internal API for comprehensive business data extraction.
"""

import logging
from typing import List, Dict, Any
from datetime import datetime

from src.scrapers.base_scraper import BaseScraper
from src.apis.manta_api import MantaAP<PERSON>
from src.core import ScrapingResult

logger = logging.getLogger(__name__)

class MantaAPIScraper(BaseScraper):
    """Scraper that uses Manta's internal API for business data."""
    
    def __init__(self, engine):
        super().__init__(engine, "manta_api")
        self.api_client = MantaAPI(self.config)
        
        logger.info("Manta API scraper initialized")
    
    def is_enabled(self) -> bool:
        """Check if Manta API scraper is enabled."""
        return self.config.get('sources', {}).get('manta_api', {}).get('enabled', False)
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """
        Search for business owners using Manta's internal API.
        
        Args:
            business_type: Type of business to search for
            location: Location to search in
            
        Returns:
            List of ScrapingResult objects with comprehensive business data
        """
        if not self.is_enabled():
            logger.info("Manta API scraper is disabled")
            return []
        
        logger.info(f"Searching Manta API for {business_type} owners in {location}")
        
        try:
            # Use the API client to search for businesses
            max_pages = self.config.get('sources', {}).get('manta_api', {}).get('max_pages', 5)
            results = self.api_client.search_businesses(business_type, location, max_pages)
            
            # Enhance results with additional processing
            enhanced_results = []
            for result in results:
                enhanced_result = self._enhance_result(result, business_type, location)
                enhanced_results.append(enhanced_result)
            
            logger.info(f"Manta API returned {len(enhanced_results)} enhanced results")
            return enhanced_results
            
        except Exception as e:
            logger.error(f"Manta API search failed: {e}")
            return []
    
    def _enhance_result(self, result: ScrapingResult, business_type: str, location: str) -> ScrapingResult:
        """Enhance a result with additional processing and data quality metrics."""
        
        # Set business type and location if not already set
        if not result.business_type:
            result.business_type = business_type
        if not result.location:
            result.location = location
        
        # Set source and quality metrics
        result.source = self.source_name
        result.data_quality = 'high'  # API data is typically high quality
        result.verification_status = 'api_verified'
        
        # Calculate confidence score based on available data
        confidence_factors = []
        
        if result.owner_name:
            confidence_factors.append(0.3)
        if result.business_name:
            confidence_factors.append(0.2)
        if result.phone:
            confidence_factors.append(0.2)
        if result.email:
            confidence_factors.append(0.15)
        if result.address:
            confidence_factors.append(0.1)
        if result.website:
            confidence_factors.append(0.05)
        
        result.confidence_score = sum(confidence_factors)
        
        # Add search metadata
        result.search_pattern = f"manta_api:{business_type}:{location}"
        
        # If we have a business URL, we could scrape additional details
        if result.url and self._should_scrape_additional_details():
            try:
                additional_data = self._scrape_business_page(result.url)
                if additional_data:
                    result = self._merge_additional_data(result, additional_data)
            except Exception as e:
                logger.debug(f"Failed to scrape additional details from {result.url}: {e}")
        
        return result
    
    def _should_scrape_additional_details(self) -> bool:
        """Determine if we should scrape additional details from business pages."""
        # This could be configurable
        return self.config.get('sources', {}).get('manta_api', {}).get('scrape_additional_details', True)
    
    def _scrape_business_page(self, url: str) -> Dict[str, Any]:
        """Scrape additional details from a business page."""
        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.content, 'html.parser')
                
                additional_data = {}
                
                # Extract additional business details
                additional_data['business_description'] = self._extract_business_description(soup)
                additional_data['years_in_business'] = self._extract_years_in_business(soup)
                additional_data['employee_count'] = self._extract_employee_count(soup)
                additional_data['annual_revenue'] = self._extract_annual_revenue(soup)
                additional_data['business_category'] = self._extract_business_category(soup)
                additional_data['social_media'] = self._extract_social_media_links(soup)
                additional_data['detailed_address'] = self._extract_detailed_address(soup)
                
                return additional_data
                
        except Exception as e:
            logger.debug(f"Error scraping business page {url}: {e}")
        
        return {}
    
    def _merge_additional_data(self, result: ScrapingResult, additional_data: Dict[str, Any]) -> ScrapingResult:
        """Merge additional scraped data into the result."""
        
        # Merge business details
        if additional_data.get('business_description') and not result.business_description:
            result.business_description = additional_data['business_description']
        
        if additional_data.get('years_in_business') and not result.years_in_business:
            result.years_in_business = additional_data['years_in_business']
        
        if additional_data.get('employee_count') and not result.employee_count:
            result.employee_count = additional_data['employee_count']
        
        if additional_data.get('annual_revenue') and not result.annual_revenue:
            result.annual_revenue = additional_data['annual_revenue']
        
        if additional_data.get('business_category') and not result.business_category:
            result.business_category = additional_data['business_category']
        
        # Merge social media links
        social_media = additional_data.get('social_media', {})
        if social_media.get('linkedin_url') and not result.linkedin_url:
            result.linkedin_url = social_media['linkedin_url']
        if social_media.get('facebook_url') and not result.facebook_url:
            result.facebook_url = social_media['facebook_url']
        if social_media.get('twitter_url') and not result.twitter_url:
            result.twitter_url = social_media['twitter_url']
        
        # Merge detailed address
        detailed_address = additional_data.get('detailed_address', {})
        if detailed_address.get('street_address') and not result.street_address:
            result.street_address = detailed_address['street_address']
        if detailed_address.get('city') and not result.city:
            result.city = detailed_address['city']
        if detailed_address.get('state') and not result.state:
            result.state = detailed_address['state']
        if detailed_address.get('zip_code') and not result.zip_code:
            result.zip_code = detailed_address['zip_code']
        
        # Update confidence score if we got more data
        if any(additional_data.values()):
            result.confidence_score = min(1.0, result.confidence_score + 0.1)
        
        return result
    
    def _extract_business_description(self, soup) -> str:
        """Extract business description from Manta page."""
        selectors = [
            '.company-description',
            '.business-description', 
            '.about-company',
            '.company-summary',
            '[class*="description"]'
        ]
        
        for selector in selectors:
            elem = soup.select_one(selector)
            if elem:
                desc = elem.get_text(strip=True)
                if len(desc) > 30:
                    return desc
        return None
    
    def _extract_years_in_business(self, soup) -> str:
        """Extract years in business from Manta page."""
        import re
        text = soup.get_text()
        
        patterns = [
            r'(\d+)\s*years?\s*in\s*business',
            r'established\s*(?:in\s*)?(\d{4})',
            r'since\s*(\d{4})',
            r'founded\s*(?:in\s*)?(\d{4})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                year_or_years = match.group(1)
                if len(year_or_years) == 4:  # It's a year
                    current_year = datetime.now().year
                    years = current_year - int(year_or_years)
                    return str(years)
                else:  # It's already years
                    return year_or_years
        return None
    
    def _extract_employee_count(self, soup) -> str:
        """Extract employee count from Manta page."""
        import re
        text = soup.get_text()
        
        patterns = [
            r'(\d+)\s*employees?',
            r'staff\s*of\s*(\d+)',
            r'(\d+)\s*(?:full-time|part-time)?\s*(?:staff|workers?)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        return None
    
    def _extract_annual_revenue(self, soup) -> str:
        """Extract annual revenue from Manta page."""
        import re
        text = soup.get_text()
        
        patterns = [
            r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:million|M)',
            r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:billion|B)',
            r'revenue:?\s*\$(\d+(?:,\d{3})*(?:\.\d{2})?)',
            r'annual\s*sales:?\s*\$(\d+(?:,\d{3})*(?:\.\d{2})?)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return f"${match.group(1)}"
        return None
    
    def _extract_business_category(self, soup) -> str:
        """Extract business category from Manta page."""
        selectors = [
            '.company-category',
            '.business-category',
            '.industry',
            '.category-name'
        ]
        
        for selector in selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text(strip=True)
        
        # Look in breadcrumbs
        breadcrumbs = soup.select('.breadcrumb a')
        if len(breadcrumbs) > 1:
            return breadcrumbs[-2].get_text(strip=True)
        
        return None
    
    def _extract_social_media_links(self, soup) -> Dict[str, str]:
        """Extract social media links from Manta page."""
        import re
        social_links = {}
        
        social_patterns = {
            'linkedin_url': r'linkedin\.com/(?:company/|in/)[^/\s]+',
            'facebook_url': r'facebook\.com/[^/\s]+',
            'twitter_url': r'twitter\.com/[^/\s]+',
            'instagram_url': r'instagram\.com/[^/\s]+',
            'youtube_url': r'youtube\.com/(?:channel/|user/|c/)[^/\s]+'
        }
        
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            for platform, pattern in social_patterns.items():
                if re.search(pattern, href, re.IGNORECASE):
                    social_links[platform] = href
                    break
        
        return social_links
    
    def _extract_detailed_address(self, soup) -> Dict[str, str]:
        """Extract detailed address components from Manta page."""
        address_info = {}
        
        # Look for structured address data
        address_selectors = [
            '.company-address',
            '.business-address',
            '.address',
            '[itemtype*="PostalAddress"]'
        ]
        
        for selector in address_selectors:
            elem = soup.select_one(selector)
            if elem:
                # Try structured data first
                street = elem.select_one('[itemprop="streetAddress"]')
                city = elem.select_one('[itemprop="addressLocality"]')
                state = elem.select_one('[itemprop="addressRegion"]')
                zip_code = elem.select_one('[itemprop="postalCode"]')
                
                if street:
                    address_info['street_address'] = street.get_text(strip=True)
                if city:
                    address_info['city'] = city.get_text(strip=True)
                if state:
                    address_info['state'] = state.get_text(strip=True)
                if zip_code:
                    address_info['zip_code'] = zip_code.get_text(strip=True)
                
                break
        
        return address_info

    def _extract_listing_urls(self, soup, business_type: str, location: str) -> List[str]:
        """Extract listing URLs from search results (required by BaseScraper)."""
        # This method is required by BaseScraper but not used in API mode
        # Return empty list since we get data directly from API
        return []

    def extract_owner_info(self, soup, url: str) -> List[ScrapingResult]:
        """Extract owner info from individual page (required by BaseScraper)."""
        # This method is required by BaseScraper but not used in API mode
        # Return empty list since we get data directly from API
        return []

    def get_health_status(self) -> Dict[str, Any]:
        """Get scraper health status."""
        try:
            api_health = self.api_client.get_health_status()
            return {
                'scraper': 'manta_api',
                'status': 'healthy' if api_health['status'] == 'healthy' else 'unhealthy',
                'api_health': api_health,
                'enabled': self.is_enabled()
            }
        except Exception as e:
            return {
                'scraper': 'manta_api',
                'status': 'unhealthy',
                'error': str(e),
                'enabled': self.is_enabled()
            }
