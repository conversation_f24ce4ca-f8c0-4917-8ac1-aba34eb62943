"""
Manta.com scraper for business owner information.
Uses Google search with site: operator as specified by client requirements.
"""

import re
import json
from typing import List
from urllib.parse import urljoin, quote_plus
import requests

from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..core import Scraping<PERSON><PERSON>ult
from ..utils.google_search import GoogleSearchEngine


class MantaScraper(BaseScraper):
    """Scraper for Manta.com business listings using Google search with site: operator."""

    def __init__(self, engine):
        super().__init__(engine, 'manta')
        self.google_search = GoogleSearchEngine(engine.config)

        # Enhanced Cloudflare bypass for Manta
        self.cloudflare_bypass_enabled = engine.config.get('sources', {}).get('manta', {}).get('cloudflare_bypass', True)
        if self.cloudflare_bypass_enabled:
            self.logger.info("Enhanced Cloudflare bypass enabled for Manta")
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search Manta for business owners using Google site: operator."""
        if not self.is_enabled():
            return []

        self.logger.info(f"Searching Manta via Google for '{business_type}' owners in '{location}'")

        results = []

        # Use Google search with site: operator as specified by client
        # Pattern: site:manta.com "Owner" "lawn care" "Dallas"
        google_results = self.google_search.search_site_for_owners('manta.com', business_type, location)

        self.logger.info(f"Found {len(google_results)} Manta listings from Google search")

        # Extract URLs and scrape each listing
        for google_result in google_results:
            url = google_result['url']
            listing_results = self.scrape_listing(url, business_type, location)
            results.extend(listing_results)

        return results
    
    def build_search_url(self, business_type: str, location: str) -> str:
        """Build Manta search URL."""
        # Manta search format: https://www.manta.com/search?search=lawn+care&city=dallas&state=tx
        encoded_query = quote_plus(f"{business_type} owner")
        
        # Parse location to extract city and state
        location_parts = location.lower().split()
        city = location_parts[0] if location_parts else location
        state = location_parts[-1] if len(location_parts) > 1 else ""
        
        return f"{self.base_url}/search?search={encoded_query}&city={city}&state={state}"
    
    def _build_page_url(self, base_url: str, page: int) -> str:
        """Build URL for specific page."""
        if page == 1:
            return base_url
        return f"{base_url}&pg={page}"
    
    def _extract_listing_urls(self, soup: BeautifulSoup) -> List[str]:
        """Extract business listing URLs from Manta search results."""
        urls = []
        
        # Manta listing selectors
        selectors = [
            'a[href*="/c/"]',  # Manta company profile URLs
            '.search-result h3 a',
            '.company-name a',
            '.business-listing a[href*="/c/"]',
            'h2 a[href*="/c/"]'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    # Convert relative URLs to absolute
                    if href.startswith('/'):
                        href = urljoin(self.base_url, href)
                    
                    # Filter for company profile URLs
                    if '/c/' in href and 'manta.com' in href:
                        urls.append(href)
        
        return urls
    
    def extract_owner_info(self, soup: BeautifulSoup, url: str) -> List[ScrapingResult]:
        """Extract owner information from Manta business page."""
        results = []
        
        # Extract business name
        business_name = self._extract_business_name(soup)
        
        # Extract owner information
        owner_names = self._extract_owner_names(soup)
        
        if owner_names:
            for owner_name in owner_names:
                result = ScrapingResult(
                    owner_name=self.clean_text(owner_name),
                    business_name=self.clean_text(business_name),
                    source=self.source_name,
                    url=url
                )
                results.append(result)
        else:
            # Create result with business info even if no owner found
            result = ScrapingResult(
                business_name=self.clean_text(business_name),
                source=self.source_name,
                url=url
            )
            results.append(result)
        
        return results
    
    def _extract_business_name(self, soup: BeautifulSoup) -> str:
        """Extract business name from Manta page."""
        selectors = [
            'h1.company-name',
            'h1[data-testid="company-name"]',
            '.company-header h1',
            'h1.business-name',
            '.page-title h1'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        # Look for JSON-LD structured data
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict) and 'name' in data:
                    return data['name']
            except:
                continue
        
        # Fallback to title tag
        title = soup.find('title')
        if title:
            title_text = title.get_text()
            # Remove Manta suffix
            title_text = re.sub(r'\s*\|\s*Manta.*$', '', title_text)
            return title_text.strip()
        
        return ""
    
    def _extract_owner_names(self, soup: BeautifulSoup) -> List[str]:
        """Extract owner names from Manta page."""
        owner_names = []
        
        # Get all text content
        page_text = soup.get_text()
        
        # Owner extraction patterns
        patterns = [
            r'Owner[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Principal[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'CEO[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'President[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Founder[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Managing Director[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Contact[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            owner_names.extend(matches)
        
        # Look for owner info in specific sections
        owner_sections = soup.select('.company-details, .contact-info, .business-info, .company-profile')
        for section in owner_sections:
            section_text = section.get_text()
            for pattern in patterns:
                matches = re.findall(pattern, section_text, re.IGNORECASE)
                owner_names.extend(matches)
        
        # Look for structured data in JSON-LD
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict):
                    # Look for founder or employee information
                    if 'founder' in data:
                        founder = data['founder']
                        if isinstance(founder, dict) and 'name' in founder:
                            owner_names.append(founder['name'])
                        elif isinstance(founder, str):
                            owner_names.append(founder)
                    
                    if 'employee' in data:
                        employees = data['employee']
                        if isinstance(employees, list):
                            for emp in employees:
                                if isinstance(emp, dict) and 'name' in emp:
                                    # Check if this is likely an owner/executive
                                    job_title = emp.get('jobTitle', '').lower()
                                    if any(title in job_title for title in ['owner', 'ceo', 'president', 'founder']):
                                        owner_names.append(emp['name'])
            except:
                continue
        
        # Look for contact person information
        contact_sections = soup.select('.contact-person, .key-contact, .primary-contact')
        for section in contact_sections:
            name_elem = section.select_one('.name, .contact-name, .person-name')
            if name_elem:
                owner_names.append(name_elem.get_text(strip=True))
        
        # Clean and deduplicate
        cleaned_names = []
        for name in owner_names:
            cleaned_name = self.clean_text(name)
            if cleaned_name and len(cleaned_name) > 2 and cleaned_name not in cleaned_names:
                # Basic validation - should be a proper name
                if re.match(r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*$', cleaned_name):
                    cleaned_names.append(cleaned_name)
        
        return cleaned_names
