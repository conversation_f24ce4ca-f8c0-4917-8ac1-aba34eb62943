#!/usr/bin/env python3
"""
Playwright API Client - Uses real browser context to make API calls.
This bypasses Cloudflare and anti-bot protection by using a real browser.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from playwright.async_api import async_playwright, <PERSON>, BrowserContext
import time

logger = logging.getLogger(__name__)

class PlaywrightAPIClient:
    """Make API calls using real browser context to bypass anti-bot protection."""
    
    def __init__(self, headless: bool = True, browser_type: str = "chromium"):
        self.headless = headless
        self.browser_type = browser_type
        self.browser = None
        self.context = None
        self.page = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def start(self):
        """Start the browser and create context."""
        self.playwright = await async_playwright().start()
        
        if self.browser_type == "chromium":
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--allow-running-insecure-content'
                ]
            )
        elif self.browser_type == "firefox":
            self.browser = await self.playwright.firefox.launch(headless=self.headless)
        else:
            self.browser = await self.playwright.webkit.launch(headless=self.headless)
        
        # Create stealth context
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York'
        )
        
        # Add stealth scripts
        await self.context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        """)
        
        self.page = await self.context.new_page()
        logger.info("Playwright browser started successfully")
    
    async def close(self):
        """Close the browser."""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
    
    async def truthfinder_search(self, first_name: str, last_name: str, city: str = "", state: str = "") -> List[Dict[str, Any]]:
        """
        Search TruthFinder using real browser interaction.
        
        Args:
            first_name: First name to search
            last_name: Last name to search  
            city: City (optional)
            state: State (optional)
            
        Returns:
            List of search results
        """
        logger.info(f"Searching TruthFinder for: {first_name} {last_name} in {city}, {state}")
        
        try:
            # Navigate to TruthFinder
            await self.page.goto("https://www.truthfinder.com", wait_until="networkidle")
            await self.page.wait_for_timeout(2000)
            
            # Fill search form
            await self.page.fill('input[name="firstName"], input[placeholder*="First"], #firstName', first_name)
            await self.page.fill('input[name="lastName"], input[placeholder*="Last"], #lastName', last_name)
            
            if city:
                city_input = await self.page.query_selector('input[name="city"], input[placeholder*="City"], #city')
                if city_input:
                    await city_input.fill(city)
            
            if state:
                state_input = await self.page.query_selector('input[name="state"], select[name="state"], #state')
                if state_input:
                    await state_input.fill(state)
            
            # Set up response interception
            results = []
            
            async def handle_response(response):
                if 'api.truthfinder.com' in response.url or 'search' in response.url:
                    try:
                        if response.status == 200:
                            data = await response.json()
                            results.append({
                                'url': response.url,
                                'data': data,
                                'timestamp': datetime.now().isoformat()
                            })
                            logger.info(f"Captured API response from: {response.url}")
                    except:
                        pass
            
            self.page.on('response', handle_response)
            
            # Submit search
            await self.page.click('button[type="submit"], .search-button, input[type="submit"]')
            
            # Wait for results
            await self.page.wait_for_timeout(5000)
            
            # Try to find results on the page
            page_results = await self._extract_page_results()
            
            return results + page_results
            
        except Exception as e:
            logger.error(f"TruthFinder search failed: {e}")
            return []
    
    async def manta_search(self, business_type: str, location: str) -> List[Dict[str, Any]]:
        """
        Search Manta using real browser interaction.
        
        Args:
            business_type: Type of business to search
            location: Location to search
            
        Returns:
            List of business results
        """
        logger.info(f"Searching Manta for: {business_type} in {location}")
        
        try:
            # Navigate to Manta
            await self.page.goto("https://www.manta.com", wait_until="networkidle")
            await self.page.wait_for_timeout(2000)
            
            # Search for businesses
            search_query = f"{business_type} {location}"
            await self.page.fill('input[name="search"], input[placeholder*="Search"], #search', search_query)
            await self.page.press('input[name="search"], input[placeholder*="Search"], #search', 'Enter')
            
            # Wait for results
            await self.page.wait_for_timeout(3000)
            
            # Extract business listings
            businesses = []
            business_elements = await self.page.query_selector_all('.business-card, .listing-item, .company-listing, [data-business-id]')
            
            for element in business_elements[:10]:  # Limit to first 10
                try:
                    business_data = await self._extract_business_element(element)
                    if business_data:
                        businesses.append(business_data)
                except Exception as e:
                    logger.debug(f"Error extracting business element: {e}")
                    continue
            
            # Try to load more results by clicking pagination or "Load More"
            try:
                load_more = await self.page.query_selector('button:has-text("Load More"), .load-more, .pagination a:last-child')
                if load_more:
                    await load_more.click()
                    await self.page.wait_for_timeout(2000)
                    
                    # Extract additional results
                    additional_elements = await self.page.query_selector_all('.business-card, .listing-item, .company-listing')
                    for element in additional_elements[len(businesses):len(businesses)+10]:
                        try:
                            business_data = await self._extract_business_element(element)
                            if business_data:
                                businesses.append(business_data)
                        except:
                            continue
            except:
                pass
            
            logger.info(f"Extracted {len(businesses)} businesses from Manta")
            return businesses
            
        except Exception as e:
            logger.error(f"Manta search failed: {e}")
            return []
    
    async def _extract_page_results(self) -> List[Dict[str, Any]]:
        """Extract results directly from the page content."""
        results = []
        
        try:
            # Look for result elements
            result_elements = await self.page.query_selector_all('.result, .person-result, .search-result, [data-person-id]')
            
            for element in result_elements:
                try:
                    result_data = {}
                    
                    # Extract name
                    name_elem = await element.query_selector('.name, .person-name, h2, h3')
                    if name_elem:
                        result_data['name'] = await name_elem.text_content()
                    
                    # Extract age
                    age_elem = await element.query_selector('.age, [class*="age"]')
                    if age_elem:
                        result_data['age'] = await age_elem.text_content()
                    
                    # Extract location
                    location_elem = await element.query_selector('.location, .address, [class*="location"]')
                    if location_elem:
                        result_data['location'] = await location_elem.text_content()
                    
                    # Extract any additional data
                    result_data['source'] = 'page_extraction'
                    result_data['timestamp'] = datetime.now().isoformat()
                    
                    if result_data.get('name'):
                        results.append(result_data)
                        
                except Exception as e:
                    logger.debug(f"Error extracting result element: {e}")
                    continue
            
        except Exception as e:
            logger.debug(f"Error extracting page results: {e}")
        
        return results
    
    async def _extract_business_element(self, element) -> Optional[Dict[str, Any]]:
        """Extract business data from a business element."""
        try:
            business_data = {}
            
            # Business name
            name_elem = await element.query_selector('.business-name, .company-name, .listing-title, h2, h3')
            if name_elem:
                business_data['business_name'] = await name_elem.text_content()
            
            # Owner/contact name
            owner_elem = await element.query_selector('.owner-name, .contact-name, .manager-name')
            if owner_elem:
                business_data['owner_name'] = await owner_elem.text_content()
            
            # Phone
            phone_elem = await element.query_selector('.phone, .tel, [href^="tel:"]')
            if phone_elem:
                phone_text = await phone_elem.text_content()
                business_data['phone'] = phone_text.strip()
            
            # Email
            email_elem = await element.query_selector('.email, [href^="mailto:"]')
            if email_elem:
                email_text = await email_elem.text_content()
                business_data['email'] = email_text.strip()
            
            # Address
            address_elem = await element.query_selector('.address, .location, .business-address')
            if address_elem:
                business_data['address'] = await address_elem.text_content()
            
            # Website
            website_elem = await element.query_selector('.website, .url, a[href*="http"]')
            if website_elem:
                href = await website_elem.get_attribute('href')
                if href and 'http' in href:
                    business_data['website'] = href
            
            # Business URL
            business_link = await element.query_selector('a[href*="/c/"], .business-link')
            if business_link:
                href = await business_link.get_attribute('href')
                if href:
                    business_data['business_url'] = href if href.startswith('http') else f"https://www.manta.com{href}"
            
            business_data['source'] = 'manta_browser'
            business_data['timestamp'] = datetime.now().isoformat()
            
            return business_data if business_data.get('business_name') else None
            
        except Exception as e:
            logger.debug(f"Error extracting business element: {e}")
            return None

# Async wrapper functions for easy integration
async def search_truthfinder_async(first_name: str, last_name: str, city: str = "", state: str = "") -> List[Dict[str, Any]]:
    """Async wrapper for TruthFinder search."""
    async with PlaywrightAPIClient(headless=True) as client:
        return await client.truthfinder_search(first_name, last_name, city, state)

async def search_manta_async(business_type: str, location: str) -> List[Dict[str, Any]]:
    """Async wrapper for Manta search."""
    async with PlaywrightAPIClient(headless=True) as client:
        return await client.manta_search(business_type, location)

# Sync wrapper functions for integration with existing code
def search_truthfinder(first_name: str, last_name: str, city: str = "", state: str = "") -> List[Dict[str, Any]]:
    """Sync wrapper for TruthFinder search."""
    return asyncio.run(search_truthfinder_async(first_name, last_name, city, state))

def search_manta(business_type: str, location: str) -> List[Dict[str, Any]]:
    """Sync wrapper for Manta search."""
    return asyncio.run(search_manta_async(business_type, location))
