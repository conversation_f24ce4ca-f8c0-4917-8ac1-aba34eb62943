"""
Google search utilities for site-specific business owner searches.
Uses Google Custom Search API for reliable results with fallback options.
"""

import requests
import time
import random
from typing import List, Dict, Optional
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
import logging

from .google_custom_search import GoogleCustomSearchAPI, GoogleSearchFallback


class GoogleSearchEngine:
    """Google search engine for site-specific business owner queries with Custom Search API."""

    def __init__(self, config: Dict):
        self.logger = logging.getLogger(__name__)
        self.config = config

        # Initialize Google Custom Search API
        google_config = config.get('google_custom_search', {})
        self.custom_search_api = GoogleCustomSearchAPI(google_config)

        # Initialize fallback search
        self.fallback_search = GoogleSearchFallback(config.get('google_search_fallback', {}))

        # Legacy support for anti_bot_config
        self.anti_bot = config.get('anti_bot', {})
        self.base_url = "https://www.google.com/search"

        # Determine which search method to use
        self.use_custom_api = self.custom_search_api.enabled

        if self.use_custom_api:
            self.logger.info("Using Google Custom Search API for searches")
        else:
            self.logger.warning("Google Custom Search API not available - using fallback")
            self.logger.warning("For better results, configure Google Custom Search API")
        
    def search_site_for_owners(self, site: str, business_type: str, location: str,
                              max_results: int = 20) -> List[Dict]:
        """
        Execute Google search with site: operator for business owners.

        Search pattern: site:bbb.org "Owner" "lawn care" "Dallas"
        Uses Google Custom Search API when available, fallback otherwise.
        """
        if self.use_custom_api:
            # Use Google Custom Search API
            return self.custom_search_api.search_site_for_owners(
                site, business_type, location, max_results
            )
        else:
            # Use fallback search (legacy method with warnings)
            return self.fallback_search.search_site_for_owners(
                site, business_type, location, max_results
            )

    def test_api_connection(self) -> bool:
        """Test Google Custom Search API connection."""
        if self.use_custom_api:
            return self.custom_search_api.test_api_connection()
        else:
            self.logger.warning("Google Custom Search API not configured")
            return False

    def get_api_status(self) -> Dict:
        """Get Google search API status."""
        if self.use_custom_api:
            return self.custom_search_api.get_api_status()
        else:
            return {
                'enabled': False,
                'reason': 'Google Custom Search API not configured',
                'setup_instructions': self.fallback_search.get_setup_instructions()
            }

    def get_quota_info(self) -> Dict:
        """Get API quota information."""
        if self.use_custom_api:
            return self.custom_search_api.get_quota_info()
        else:
            return {
                'available': False,
                'message': 'Configure Google Custom Search API to see quota information'
            }
    
    def _get_google_headers(self) -> Dict[str, str]:
        """Get headers optimized for Google search."""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
    
    def _parse_google_results(self, html: str, target_site: str) -> List[Dict]:
        """Parse Google search results HTML."""
        results = []
        soup = BeautifulSoup(html, 'lxml')
        
        # Google search result selectors
        result_selectors = [
            'div.g',  # Standard result container
            'div[data-ved]',  # Alternative result container
            '.rc'  # Classic result container
        ]
        
        for selector in result_selectors:
            search_results = soup.select(selector)
            
            for result_div in search_results:
                # Extract link
                link_elem = result_div.select_one('h3 a, a h3, .yuRUbf a')
                if not link_elem:
                    continue
                
                url = link_elem.get('href')
                if not url or target_site not in url:
                    continue
                
                # Extract title
                title_elem = result_div.select_one('h3')
                title = title_elem.get_text(strip=True) if title_elem else ""
                
                # Extract snippet
                snippet_elem = result_div.select_one('.VwiC3b, .s3v9rd, .st, .IsZvec')
                snippet = snippet_elem.get_text(strip=True) if snippet_elem else ""
                
                results.append({
                    'url': url,
                    'title': title,
                    'snippet': snippet,
                    'source_site': target_site
                })
        
        return results
    
    def search_all_sites(self, business_type: str, location: str) -> Dict[str, List[Dict]]:
        """
        Search all target sites using Google site: operator.
        
        Returns results grouped by site.
        """
        target_sites = [
            'bbb.org',
            'manta.com', 
            'linkedin.com'
        ]
        
        all_results = {}
        
        for site in target_sites:
            self.logger.info(f"Searching {site} via Google...")
            
            site_results = self.search_site_for_owners(site, business_type, location)
            all_results[site] = site_results
            
            # Delay between site searches
            time.sleep(random.uniform(3, 7))
        
        return all_results
