"""
TruthFinder API integration for people search functionality.
Replaces web scraping with reliable API calls.
"""

import requests
import logging
import time
from typing import List, Dict, Optional
from datetime import datetime
import json

try:
    from ..core import ScrapingResult
except ImportError:
    # Fallback for direct testing
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core import ScrapingResult


class TruthFinderAPI:
    """TruthFinder API client for people search."""

    def __init__(self, config: Dict):
        """Initialize TruthFinder API client with configuration."""
        self.api_key = config.get('api_key', '')
        self.app_id = config.get('app_id', 'tf-web')
        self.base_url = config.get('base_url', 'https://api.truthfinder.com')
        self.rate_limit = config.get('rate_limit', 100)  # requests per minute
        self.timeout = config.get('timeout', 30)
        self.enabled = config.get('enabled', True)

        self.logger = logging.getLogger(__name__)
        self.last_request_time = 0
        self.request_count = 0
        self.request_window_start = time.time()

        # Headers based on the actual API documentation
        self.headers = {
            'api-key': self.api_key,
            'app-id': self.app_id,
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'accept': 'application/json',
            'origin': 'https://www.truthfinder.com'
        }

        if not self.api_key:
            self.logger.warning("TruthFinder API key not provided - API will be disabled")
            self.enabled = False
    
    def _check_rate_limit(self):
        """Enforce rate limiting for API requests."""
        current_time = time.time()

        # Reset counter if window has passed (1 minute)
        if current_time - self.request_window_start >= 60:
            self.request_count = 0
            self.request_window_start = current_time

        # Check if we've exceeded rate limit
        if self.request_count >= self.rate_limit:
            sleep_time = 60 - (current_time - self.request_window_start)
            if sleep_time > 0:
                self.logger.info(f"Rate limit reached, sleeping for {sleep_time:.1f} seconds")
                time.sleep(sleep_time)
                self.request_count = 0
                self.request_window_start = time.time()

        self.request_count += 1
        self.last_request_time = current_time

    def search_business_owners(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search for business owners using TruthFinder API."""
        if not self.enabled:
            self.logger.warning("TruthFinder API is disabled")
            return []

        try:
            self._check_rate_limit()

            # Parse location
            location_parts = location.strip().split()
            if len(location_parts) >= 2:
                state = location_parts[-1].upper()
                city = " ".join(location_parts[:-1]).title()
            else:
                city = location.title()
                state = ""

            self.logger.info(f"Searching TruthFinder API for {business_type} owners in {city}, {state}")

            # Use the actual API endpoint structure from the documentation
            # The API appears to search by name, so we'll search for common business owner names
            # in the specified location and filter by business type
            search_results = []

            # Common business owner search terms
            owner_search_terms = [
                f"{business_type} owner {city}",
                f"{business_type} business {city}",
                f"owner {business_type} {city}"
            ]

            for search_term in owner_search_terms:
                try:
                    # Make API request based on the documented structure
                    search_params = {
                        "firstName": "",
                        "lastName": search_term,
                        "city": city,
                        "state": state,
                        "age": "",
                        "includeRelatives": True
                    }

                    response = requests.post(
                        f"{self.base_url}/search",
                        headers=self.headers,
                        json=search_params,
                        timeout=self.timeout
                    )

                    if response.status_code == 200:
                        data = response.json()
                        if data:  # If we got results
                            parsed_results = self._parse_api_response(data, business_type, location)
                            search_results.extend(parsed_results)

                            # Limit results per search term to avoid overwhelming
                            if len(search_results) >= 10:
                                break
                    else:
                        self.logger.warning(f"API request failed with status {response.status_code}: {response.text}")

                except Exception as e:
                    self.logger.error(f"Error in search term '{search_term}': {e}")
                    continue

            self.logger.info(f"TruthFinder API returned {len(search_results)} results")
            return search_results[:10]  # Limit to top 10 results

        except Exception as e:
            self.logger.error(f"TruthFinder API error: {e}")
            return []
    
    def _parse_api_response(self, data: List[Dict], business_type: str, location: str) -> List[ScrapingResult]:
        """Parse TruthFinder API response into ScrapingResult objects."""
        results = []

        # Handle both list and single object responses
        if isinstance(data, dict):
            data = [data]

        for person_data in data:
            try:
                # Extract names
                names = person_data.get('names', [])
                if not names:
                    continue

                primary_name = names[0]
                first_name = primary_name.get('first', '').strip()
                middle_name = primary_name.get('middle', '').strip()
                last_name = primary_name.get('last', '').strip()

                # Build full name
                name_parts = [first_name, middle_name, last_name]
                full_name = " ".join([part for part in name_parts if part])

                if not full_name:
                    continue

                # Extract locations and address
                locations = person_data.get('locations', [])
                address_info = None
                person_city = None
                person_state = None

                if locations:
                    address_data = locations[0].get('address', {})
                    street = address_data.get('street', '').replace('*', '').strip()
                    person_city = address_data.get('city', '').strip()
                    person_state = address_data.get('state', '').strip()
                    zip_code = address_data.get('zip_code', '').replace('*', '').strip()

                    # Build address string
                    address_parts = []
                    if street:
                        address_parts.append(street)
                    if person_city:
                        address_parts.append(person_city)
                    if person_state:
                        address_parts.append(person_state)
                    if zip_code:
                        address_parts.append(zip_code)

                    if address_parts:
                        address_info = ", ".join(address_parts)

                # Extract age information
                age_info = None
                dobs = person_data.get('dobs', [])
                if dobs:
                    age_data = dobs[0]
                    age_info = age_data.get('age')

                # Generate business name (inferred from owner name and business type)
                business_name = f"{first_name}'s {business_type.title()}"
                if business_type.lower() in ['restaurant', 'construction', 'plumbing', 'electrical']:
                    business_name += " Services"
                elif business_type.lower() == 'auto repair':
                    business_name = f"{first_name}'s Auto Repair"
                elif business_type.lower() == 'lawn care':
                    business_name = f"{first_name}'s Lawn Care"

                # Calculate confidence score based on data completeness
                confidence_score = 0.7  # Base score
                if address_info:
                    confidence_score += 0.1
                if age_info:
                    confidence_score += 0.1
                if len(names) > 1:  # Multiple name records
                    confidence_score += 0.1

                # Create result
                result = ScrapingResult(
                    owner_name=full_name,
                    business_name=business_name,
                    business_type=business_type,
                    location=location,
                    source="truthfinder_api",
                    address=address_info,
                    scraped_at=datetime.now(),
                    raw_data={
                        'api_response': person_data,
                        'data_quality': 'high',
                        'confidence_score': confidence_score,
                        'api_source': 'truthfinder',
                        'person_city': person_city,
                        'person_state': person_state,
                        'age': age_info,
                        'search_method': 'api'
                    }
                )

                results.append(result)

            except Exception as e:
                self.logger.error(f"Error parsing person data: {e}")
                continue

        return results
    
    def get_person_details(self, person_id: str) -> Optional[Dict]:
        """Get detailed information for a specific person."""
        if not self.enabled:
            return None

        try:
            self._check_rate_limit()

            response = requests.get(
                f"{self.base_url}/person/{person_id}",
                headers=self.headers,
                timeout=self.timeout
            )

            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Person details request failed: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting person details: {e}")
            return None

    def test_api_connection(self) -> bool:
        """Test if the API connection is working."""
        if not self.enabled:
            self.logger.warning("TruthFinder API is disabled")
            return False

        try:
            # Test with a simple search
            test_params = {
                "firstName": "John",
                "lastName": "Smith",
                "city": "Houston",
                "state": "TX"
            }

            response = requests.post(
                f"{self.base_url}/search",
                headers=self.headers,
                json=test_params,
                timeout=10
            )

            if response.status_code == 200:
                self.logger.info("TruthFinder API connection test successful")
                return True
            else:
                self.logger.error(f"API connection test failed: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"API connection test error: {e}")
            return False

    def get_api_status(self) -> Dict:
        """Get current API status and usage information."""
        return {
            'enabled': self.enabled,
            'api_key_configured': bool(self.api_key),
            'base_url': self.base_url,
            'rate_limit': self.rate_limit,
            'requests_made': self.request_count,
            'last_request': self.last_request_time,
            'window_start': self.request_window_start
        }
